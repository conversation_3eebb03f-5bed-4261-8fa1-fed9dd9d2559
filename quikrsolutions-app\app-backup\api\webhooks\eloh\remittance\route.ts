import { NextResponse } from 'next/server';
import crypto from 'crypto';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

const ELOH_WEBHOOK_SECRET = process.env.ELOH_WEBHOOK_SECRET; // Use the same secret as for crypto payments

async function verifyWebhookSignature(request: Request, signature: string | null, sharedSecret: string): Promise<boolean> {
  if (!signature) {
    console.warn('Missing signature');
    return false; // Missing signature
  }

  const body = await request.text(); // Get raw request body

  const hmac = crypto.createHmac('sha256', sharedSecret);
  hmac.update(body);
  const calculatedSignature = hmac.digest('hex');

  // Timing-safe comparison
  try {
    return crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(calculatedSignature, 'hex'));
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
}

export async function POST(request: Request) {
  const signature = request.headers.get('X-Eloh-Signature'); // Assuming Eloh uses the same header for remittances
  const sharedSecret = ELOH_WEBHOOK_SECRET;

  if (!sharedSecret) {
    console.error('Missing ELOH_WEBHOOK_SECRET');
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const isValid = await verifyWebhookSignature(request, signature, sharedSecret);

  if (!isValid) {
    console.warn('Invalid webhook signature');
    return new Response(JSON.stringify({ error: 'Invalid signature' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    const body = await request.json();
    const { eventType, data } = body;

    console.log('Received Eloh remittance webhook event:', { eventType, data });

    const supabaseAdmin = createSupabaseAdminClient();

    // Assuming the webhook data includes eloh_remittance_id or quikrsolutions_remittance_id
    const { elohRemittanceId, quikrsolutionsRemittanceId, status } = data;

    if (!elohRemittanceId && !quikrsolutionsRemittanceId) {
        console.error('Webhook data missing remittance ID:', data);
         return new Response(JSON.stringify({ error: 'Missing remittance ID in webhook data' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
    }

    // Determine which ID to use for lookup
    const lookupId = quikrsolutionsRemittanceId || elohRemittanceId;
    const lookupColumn = quikrsolutionsRemittanceId ? 'quikrsolutions_remittance_id' : 'eloh_remittance_id';


    // Update the remittance transaction status in the database
    const { data: updateData, error: updateError } = await supabaseAdmin
      .from('remittance_transactions')
      .update({ status: status })
      .eq(lookupColumn, lookupId).select().single(); // Use select().single() to get the updated row

    if (updateError) {
      console.error('Error updating remittance transaction status:', updateError);
       return new Response(JSON.stringify({ error: 'Database update failed', details: updateError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    if (!updateData) {
        console.warn('Remittance transaction not found for update:', lookupColumn, lookupId);
         return new Response(JSON.stringify({ error: 'Remittance transaction not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
    }


    console.log('Remittance status updated successfully:', updateData);

    // TODO: Implement any post-remittance processing logic here (e.g., notifying sender/recipient)


    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error: any) {
    console.error('Error processing remittance webhook:', error);
    return new Response(JSON.stringify({ error: 'Webhook processing failed', details: error.message }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
