// src/lib/services/eloh/elohCryptoService.ts

import {
  InitiateElohCryptoPaymentParams,
  InitiateElohCryptoPaymentResult,
  ElohErrorResponse,
} from './elohTypes';

const MOCK_ELOH_API_BASE_URL = '/api/mock-eloh/v1'; // Or process.env.ELOH_API_BASE_URL for real Eloh

export async function initiateCryptoPayment(
  params: InitiateElohCryptoPaymentParams
): Promise<InitiateElohCryptoPaymentResult> {
  try {
    const response = await fetch(
      `${MOCK_ELOH_API_BASE_URL}/btcpay/invoices`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add API key when integrating with real Eloh
          // 'X-API-Key': process.env.ELOH_API_KEY ?? '',
        },
        body: JSON.stringify(params),
      }
    );

    const data = await response.json();

    if (response.ok) {
      // Assuming the mock API returns data that matches ElohCryptoPaymentSuccessResponse['data']
      return { status: 'success', data: data };
    } else {
      return data as ElohErrorResponse;
    }
  } catch (error: any) {
    console.error('Error initiating crypto payment:', error);
    return {
      status: 'error',
      message: 'Failed to initiate crypto payment',
      details: {
        error: error.message,
      },
    };
  }
}
