{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/lib/supabase/server.ts"], "sourcesContent": ["// src/lib/supabase/server.ts\r\nimport { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { cookies } from 'next/headers';\r\nimport { createClient } from '@supabase/supabase-js'; // Changed from require\r\n\r\nexport { createServerClient } from '@supabase/ssr'; // Export createServerClient\r\n\r\n// For Server Components, Route Handlers\r\nexport async function createSupabaseServerClient() {\r\n  const cookieStore = await cookies();\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          cookieStore.set({ name, value, ...options });\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          cookieStore.set({ name, value: '', ...options });\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// For Route Handlers and Server Actions where you pass the cookie store explicitly,\r\n// for example, in a Server Action where `cookies()` might not be directly available\r\n// or when working with specific cookie manipulation logic.\r\n// Reverting to a simpler type for cookieStore and using set for remove.\r\nexport async function createSupabaseServerClientWithCookies(cookieStore: any) { // Changed any to any as per linter error\r\n    return createServerClient(\r\n        process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n        {\r\n            cookies: {\r\n                get(name: string) {\r\n                return cookieStore.get(name)?.value\r\n                },\r\n                set(name: string, value: string, options: CookieOptions) {\r\n                cookieStore.set({ name, value, ...options })\r\n                },\r\n                remove(name: string, options: CookieOptions) {\r\n                cookieStore.set({ name, value: '', ...options })\r\n                },\r\n            },\r\n        }\r\n    )\r\n}\r\n\r\n// For Admin-level operations\r\nexport function createSupabaseAdminClient() {\r\n  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {\r\n    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined. This client is for admin operations only.');\r\n  }\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        persistSession: false,\r\n        autoRefreshToken: false,\r\n      },\r\n      global: {\r\n        headers: { 'x-supabase-api-key': process.env.SUPABASE_SERVICE_ROLE_KEY },\r\n      },\r\n    }\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;AAC7B;AAAA;AACA;AACA,4TAAsD,uBAAuB;;;;;AAKtE,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAsB;gBACrD,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC5C;YACA,QAAO,IAAY,EAAE,OAAsB;gBACzC,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAChD;QACF;IACF;AAEJ;AAMO,eAAe,sCAAsC,WAAgB;IACxE,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGpB;QACI,SAAS;YACL,KAAI,IAAY;gBAChB,OAAO,YAAY,GAAG,CAAC,OAAO;YAC9B;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAsB;gBACvD,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC1C;YACA,QAAO,IAAY,EAAE,OAAsB;gBAC3C,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAC9C;QACJ;IACJ;AAER;AAGO,SAAS;IACd,IAAI,CAAC,QAAQ,GAAG,CAAC,yBAAyB,EAAE;QAC1C,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,gFAEhB,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,MAAM;YACJ,gBAAgB;YAChB,kBAAkB;QACpB;QACA,QAAQ;YACN,SAAS;gBAAE,sBAAsB,QAAQ,GAAG,CAAC,yBAAyB;YAAC;QACzE;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/app/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation'\nimport { createSupabaseServerClient } from '@/lib/supabase/server'\n\nexport default async function Home() {\n  const supabase = createSupabaseServerClient()\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  if (user) {\n    redirect('/app/feed')\n  } else {\n    redirect('/auth/login')\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEe,eAAe;IAC5B,MAAM,WAAW,CAAA,GAAA,gJAAA,CAAA,6BAA0B,AAAD;IAE1C,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,MAAM;QACR,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX,OAAO;QACL,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;AACF", "debugId": null}}]}