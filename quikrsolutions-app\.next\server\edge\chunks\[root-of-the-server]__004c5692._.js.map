{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/supabase/server.ts"], "sourcesContent": ["// src/lib/supabase/server.ts\r\nimport { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { cookies } from 'next/headers';\r\nimport { createClient } from '@supabase/supabase-js'; // Changed from require\r\n\r\nexport { createServerClient } from '@supabase/ssr'; // Export createServerClient\r\n\r\n// For Server Components, Route Handlers\r\nexport async function createSupabaseServerClient() {\r\n  const cookieStore = await cookies();\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          cookieStore.set({ name, value, ...options });\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          cookieStore.set({ name, value: '', ...options });\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// For Route Handlers and Server Actions where you pass the cookie store explicitly,\r\n// for example, in a Server Action where `cookies()` might not be directly available\r\n// or when working with specific cookie manipulation logic.\r\n// Reverting to a simpler type for cookieStore and using set for remove.\r\nexport async function createSupabaseServerClientWithCookies(cookieStore: any) { // Changed any to any as per linter error\r\n    return createServerClient(\r\n        process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n        {\r\n            cookies: {\r\n                get(name: string) {\r\n                return cookieStore.get(name)?.value\r\n                },\r\n                set(name: string, value: string, options: CookieOptions) {\r\n                cookieStore.set({ name, value, ...options })\r\n                },\r\n                remove(name: string, options: CookieOptions) {\r\n                cookieStore.set({ name, value: '', ...options })\r\n                },\r\n            },\r\n        }\r\n    )\r\n}\r\n\r\n// For Admin-level operations\r\nexport function createSupabaseAdminClient() {\r\n  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {\r\n    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined. This client is for admin operations only.');\r\n  }\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        persistSession: false,\r\n        autoRefreshToken: false,\r\n      },\r\n      global: {\r\n        headers: { 'x-supabase-api-key': process.env.SUPABASE_SERVICE_ROLE_KEY },\r\n      },\r\n    }\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;AAC7B;AAAA;AACA;AAAA;AACA,4UAAsD,uBAAuB;;;;;AAKtE,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAChC,OAAO,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAsB;gBACrD,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC5C;YACA,QAAO,IAAY,EAAE,OAAsB;gBACzC,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAChD;QACF;IACF;AAEJ;AAMO,eAAe,sCAAsC,WAAgB;IACxE,OAAO,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,sUAGpB;QACI,SAAS;YACL,KAAI,IAAY;gBAChB,OAAO,YAAY,GAAG,CAAC,OAAO;YAC9B;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAsB;gBACvD,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC1C;YACA,QAAO,IAAY,EAAE,OAAsB;gBAC3C,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAC9C;QACJ;IACJ;AAER;AAGO,SAAS;IACd,IAAI,CAAC,QAAQ,GAAG,CAAC,yBAAyB,EAAE;QAC1C,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,CAAA,GAAA,+LAAA,CAAA,eAAY,AAAD,gFAEhB,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,MAAM;YACJ,gBAAgB;YAChB,kBAAkB;QACpB;QACA,QAAQ;YACN,SAAS;gBAAE,sBAAsB,QAAQ,GAAG,CAAC,yBAAyB;YAAC;QACzE;IACF;AAEJ"}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createServerClient } from './lib/supabase/server'; // Corrected import path\r\nimport { NextResponse, type NextRequest } from 'next/server';\r\nimport { type CookieOptions } from '@supabase/ssr'; // Import CookieOptions\r\n\r\n// src/middleware.ts\r\n// ... (imports) ...\r\nexport async function middleware(request: NextRequest) {\r\n  console.log('--- Middleware running for path:', request.nextUrl.pathname);\r\n\r\n  let response = NextResponse.next({\r\n    request: {\r\n      headers: request.headers,\r\n    },\r\n  });\r\n\r\n  const supabase = createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get: (name: string) => request.cookies.get(name)?.value,\r\n        set: (name: string, value: string, options: CookieOptions) => { // Changed any to CookieOptions\r\n          request.cookies.set({ name, value, ...options });\r\n          response = NextResponse.next({\r\n            request: {\r\n              headers: request.headers,\r\n            },\r\n          });\r\n          response.cookies.set({ name, value, ...options });\r\n        },\r\n        remove: (name: string, options: CookieOptions) => { // Changed any to <PERSON>ieOptions\r\n          request.cookies.set({ name, value: '', ...options });\r\n          response = NextResponse.next({\r\n            request: {\r\n              headers: request.headers,\r\n            },\r\n          });\r\n          response.cookies.set({ name, value: '', ...options });\r\n        },\r\n      },\r\n    }\r\n  );\r\n\r\n  const { data: { session } } = await supabase.auth.getSession(); // Crucial: This fetches the session recognized by the server\r\n  console.log('Middleware session status:', session ? `User ID: ${session.user.id}` : 'No session');\r\n\r\n  const { pathname } = request.nextUrl;\r\n\r\n  const authRoutes = ['/login', '/signup']; // Define auth routes\r\n\r\n  const isAuthRoute = authRoutes.includes(pathname);\r\n\r\n  if (session && isAuthRoute) {\r\n    console.log(`Middleware: Session exists (User: ${session.user.id}) AND on auth route (${pathname}). Redirecting to /dashboard.`);\r\n    return NextResponse.redirect(new URL('/dashboard', request.url));\r\n  }\r\n  // ... (other conditions) ...\r\n  console.log('Middleware: No redirect condition met. Proceeding.');\r\n  return response;\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     */\r\n    '/((?!api|_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA,+RAA4D,wBAAwB;AAApF;AACA;AAAA;;;AAKO,eAAe,WAAW,OAAoB;IACnD,QAAQ,GAAG,CAAC,oCAAoC,QAAQ,OAAO,CAAC,QAAQ;IAExE,IAAI,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC/B,SAAS;YACP,SAAS,QAAQ,OAAO;QAC1B;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,sUAGhC;QACE,SAAS;YACP,KAAK,CAAC,OAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC,OAAO;YAClD,KAAK,CAAC,MAAc,OAAe;gBACjC,QAAQ,OAAO,CAAC,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;gBAC9C,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAC3B,SAAS;wBACP,SAAS,QAAQ,OAAO;oBAC1B;gBACF;gBACA,SAAS,OAAO,CAAC,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YACjD;YACA,QAAQ,CAAC,MAAc;gBACrB,QAAQ,OAAO,CAAC,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;gBAClD,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAC3B,SAAS;wBACP,SAAS,QAAQ,OAAO;oBAC1B;gBACF;gBACA,SAAS,OAAO,CAAC,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YACrD;QACF;IACF;IAGF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,IAAI,6DAA6D;IAC7H,QAAQ,GAAG,CAAC,8BAA8B,UAAU,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,GAAG;IAEpF,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,MAAM,aAAa;QAAC;QAAU;KAAU,EAAE,qBAAqB;IAE/D,MAAM,cAAc,WAAW,QAAQ,CAAC;IAExC,IAAI,WAAW,aAAa;QAC1B,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,QAAQ,IAAI,CAAC,EAAE,CAAC,qBAAqB,EAAE,SAAS,6BAA6B,CAAC;QAC/H,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IACA,6BAA6B;IAC7B,QAAQ,GAAG,CAAC;IACZ,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}