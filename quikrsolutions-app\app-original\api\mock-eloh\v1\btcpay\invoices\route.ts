import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { createSupabaseAdminClient } from '@/lib/supabase/server'; // Assuming this client uses SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY
import { Database } from '@/types/supabase'; // Import your database types

// Placeholder for Eloh API key validation (if needed)
const ELOH_API_KEY = process.env.ELOH_API_KEY;

export async function POST(request: Request) {
  // if (!ELOH_API_KEY) {
  //   return NextResponse.json({ status: 'error', message: 'Missing ELOH_API_KEY' }, { status: 500 });
  // }

  try {
    const body = await request.json();

    // Extract data from the request body, including userId
    const { amountFiat, fiatCurrency, description, buyerEmail, redirectUrl, notificationUrl, userId } = body;

    // Basic validation
    if (!amountFiat || !fiatCurrency || !description || !userId) {
      console.error('Mock Eloh: Invalid request parameters received:', {
        amountFiat,
        fiatCurrency,
        description,
        buyerEmail,
        redirectUrl,
        notificationUrl,
        userId
      });
      return NextResponse.json({ status: 'error', message: 'Invalid request parameters' }, { status: 400 });
    }

    // Generate quikrsolutions_order_id on the server
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const quikrsolutionsOrderId = `QSCPT-${timestamp}-${randomString}`;

    // Generate mock IDs
    const elohTransactionId = `eloh_tx_${uuidv4()}`;
    const btcpayInvoiceId = `inv_${uuidv4()}`;
    const btcpayInvoiceUrl = `${process.env.NEXT_PUBLIC_APP_URL}/payment/eloh/success?orderId=${quikrsolutionsOrderId}&userId=${userId}`; // Use app URL for redirect
    const expiresAt = new Date(Date.now() + 3600000).toISOString(); // Expires in 1 hour

    // Use the admin client to insert the initial transaction record
    const supabaseAdmin = createSupabaseAdminClient();

    const insertPayload = {
      user_id: userId, // Use the userId passed from the frontend
      quikrsolutions_order_id: quikrsolutionsOrderId,
      btcpay_invoice_id: btcpayInvoiceId,
      eloh_transaction_id: elohTransactionId,
      status: 'pending_payment',
      amount_fiat: amountFiat,
      fiat_currency: fiatCurrency,
    };

    console.log('Mock Eloh: Attempting to insert transaction record with payload:', insertPayload);

    const { data: insertData, error: insertError } = await supabaseAdmin
      .from('eloh_crypto_transactions')
      .insert([insertPayload]).select().single();

    if (insertError) {
      console.error('Mock Eloh: Error inserting initial transaction record:', insertError);
      console.error('Mock Eloh: Full insertion error details:', JSON.stringify(insertError, null, 2));
      return NextResponse.json({ status: 'error', message: 'Failed to create transaction record', details: insertError.message }, { status: 500 });
    }

    console.log('Mock Eloh: Inserted initial transaction record:', insertData);

    // Log what would happen in a real implementation
    console.log('Mock Eloh: Received invoice request:', {
      amountFiat,
      fiatCurrency,
      description,
      quikrsolutionsOrderId,
      buyerEmail,
      redirectUrl,
      notificationUrl,
      userId
    });
    console.log('Mock Eloh: Would create BTCPay invoice with amount', amountFiat, fiatCurrency);
    console.log('Mock Eloh: Would store transaction details in database');

    // Create the success response
    const responseBody = {
      status: 'success',
      data: {
        elohTransactionId,
        btcpayInvoiceId,
        btcpayInvoiceUrl,
        expiresAt,
        amountCrypto: '0.00025', // Mock crypto amount
        cryptoCurrency: 'BTC', // Mock crypto currency
        quikrsolutionsOrderId: quikrsolutionsOrderId, // Include in response
      },
    };

    return NextResponse.json(responseBody, { status: 200 });
  } catch (error: any) {
    console.error('Mock Eloh: Error processing invoice request:', error);
    return NextResponse.json({ status: 'error', message: 'Internal server error', details: error.message }, { status: 500 });
  }
}
