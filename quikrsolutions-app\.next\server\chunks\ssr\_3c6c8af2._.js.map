{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/use-toast.ts"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { ToastProps } from \"@/components/ui/toast\" // Assuming ToastProps is exported from toast.tsx\r\n\r\nconst TOAST_LIMIT = 1\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: React.ReactNode\r\n}\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n} as const\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString()\r\n}\r\n\r\ntype ActionType = typeof actionTypes\r\n\r\ntype Action =\r\n  | {\r\n      type: ActionType[\"ADD_TOAST\"]\r\n      toast: ToasterToast\r\n    }\r\n  | {\r\n      type: ActionType[\"UPDATE_TOAST\"]\r\n      toast: Partial<ToasterToast>\r\n    }\r\n  | {\r\n      type: ActionType[\"DISMISS_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n  | {\r\n      type: ActionType[\"REMOVE_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case actionTypes.ADD_TOAST:\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case actionTypes.UPDATE_TOAST:\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      }\r\n\r\n    case actionTypes.DISMISS_TOAST: {\r\n      const { toastId } = action\r\n\r\n      // Doing this as the `dismiss` action can be called on a toast that's already been removed.\r\n      if (toastId) {\r\n        return {\r\n          ...state,\r\n          toasts: state.toasts.map((t) =>\r\n            t.id === toastId ? { ...t, open: false } : t\r\n          ),\r\n        }\r\n      } else {\r\n        return {\r\n          ...state,\r\n          toasts: state.toasts.map((t) => ({\r\n            ...t,\r\n            open: false,\r\n          })),\r\n        }\r\n      }\r\n    }\r\n    case actionTypes.REMOVE_TOAST:\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        };\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n    default:\r\n      return state\r\n  }\r\n}\r\n\r\nconst listeners: ((state: State) => void)[] = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => listener(memoryState))\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId()\r\n\r\n  const update = (props: Partial<ToasterToast>) =>\r\n    dispatch({ type: actionTypes.UPDATE_TOAST, toast: { ...props, id } })\r\n  const dismiss = () => dispatch({ type: actionTypes.DISMISS_TOAST, toastId: id })\r\n\r\n  dispatch({\r\n    type: actionTypes.ADD_TOAST,\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) {\r\n          dismiss()\r\n        }\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    }\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId?: string) => dispatch({ type: actionTypes.DISMISS_TOAST, toastId }),\r\n  }\r\n}\r\n\r\nexport { useToast, toast }\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAMA,MAAM,cAAc;AASpB,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,UAAU,CAAC,OAAc;IAC7B,OAAQ,OAAO,IAAI;QACjB,KAAK,YAAY,SAAS;YACxB,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK,YAAY,YAAY;YAC3B,OAAO;gBACL,GAAG,KAAK;gBA<PERSON>,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK,YAAY,aAAa;YAAE;gBAC9B,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2FAA2F;gBAC3F,IAAI,SAAS;oBACX,OAAO;wBACL,GAAG,KAAK;wBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,UAAU;gCAAE,GAAG,CAAC;gCAAE,MAAM;4BAAM,IAAI;oBAE/C;gBACF,OAAO;oBACL,OAAO;wBACL,GAAG,KAAK;wBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;gCAC/B,GAAG,CAAC;gCACJ,MAAM;4BACR,CAAC;oBACH;gBACF;YACF;QACA,KAAK,YAAY,YAAY;YAC3B,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;QACF;YACE,OAAO;IACX;AACF;AAEA,MAAM,YAAwC,EAAE;AAEhD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS;AAC3C;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YAAE,MAAM,YAAY,YAAY;YAAE,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QAAE;IACrE,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM,YAAY,aAAa;YAAE,SAAS;QAAG;IAE9E,SAAS;QACP,MAAM,YAAY,SAAS;QAC3B,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;oBACT;gBACF;YACF;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM,YAAY,aAAa;gBAAE;YAAQ;IACrF;AACF", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/subscriptions/PayWithCryptoButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useState } from \"react\";\r\nimport { useToast } from \"@/components/ui/use-toast\";\r\n\r\ninterface PayWithCryptoButtonProps {\r\n  amount: number;\r\n  currency: string;\r\n  planId: string;\r\n  userId: string;\r\n  onOrderCreated: (orderId: string) => void;\r\n}\r\n\r\nconst PayWithCryptoButton: React.FC<PayWithCryptoButtonProps> = ({ amount, currency, planId, userId, onOrderCreated }) => {\r\n  const [isCryptoPaymentPending, setIsCryptoPaymentPending] = useState(false);\r\n\r\n  const { toast } = useToast();\r\n\r\n  const handlePayWithCrypto = async () => {\r\n    setIsCryptoPaymentPending(true);\r\n\r\n    try {\r\n      const response = await fetch('/api/mock-eloh/v1/btcpay/invoices', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          amountFiat: amount,\r\n          fiatCurrency: currency,\r\n          description: `Payment for plan ${planId || 'subscription'}`,\r\n          buyerEmail: '<EMAIL>', // Replace with actual email\r\n          redirectUrl: `${window.location.origin}/payment/eloh/success?userId=${userId}`, // Remove orderId from here\r\n          notificationUrl: `${window.location.origin}/api/webhooks/eloh/btcpay`,\r\n          userId: userId\r\n        }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        let errorMessage = `Payment initiation failed. Status: ${response.status}`;\r\n        try {\r\n          const errorData = await response.json();\r\n          errorMessage += ` - ${errorData.message || 'No details provided'}`;\r\n        } catch (parseError) {\r\n          console.warn(\"Failed to parse error response:\", parseError);\r\n        }\r\n        throw new Error(errorMessage);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.status === 'success') {\r\n        console.log('Redirecting to:', data.data.btcpayInvoiceUrl);\r\n        onOrderCreated(data.data.quikrsolutionsOrderId); // Get orderId from response\r\n        window.location.href = data.data.btcpayInvoiceUrl;\r\n      } else {\r\n        console.error('Eloh API error:', data);\r\n        throw new Error(`Eloh API Error: ${data.message || 'Unknown error'}`);\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error calling Eloh API:', error);\r\n      toast({\r\n        title: \"Payment Error\",\r\n        description: error.message,\r\n        variant: \"destructive\",\r\n      });\r\n    } finally {\r\n      setIsCryptoPaymentPending(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Button onClick={handlePayWithCrypto} disabled={isCryptoPaymentPending}>\r\n      {isCryptoPaymentPending ? \"Redirecting to Crypto Payment...\" : \"Pay with Crypto\"}\r\n    </Button>\r\n  );\r\n};\r\n\r\nexport default PayWithCryptoButton;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,sBAA0D,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE;IACnH,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,sBAAsB;QAC1B,0BAA0B;QAE1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY;oBACZ,cAAc;oBACd,aAAa,CAAC,iBAAiB,EAAE,UAAU,gBAAgB;oBAC3D,YAAY;oBACZ,aAAa,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,6BAA6B,EAAE,QAAQ;oBAC9E,iBAAiB,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,yBAAyB,CAAC;oBACrE,QAAQ;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI,eAAe,CAAC,mCAAmC,EAAE,SAAS,MAAM,EAAE;gBAC1E,IAAI;oBACF,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,gBAAgB,CAAC,GAAG,EAAE,UAAU,OAAO,IAAI,uBAAuB;gBACpE,EAAE,OAAO,YAAY;oBACnB,QAAQ,IAAI,CAAC,mCAAmC;gBAClD;gBACA,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,MAAM,KAAK,WAAW;gBAC7B,QAAQ,GAAG,CAAC,mBAAmB,KAAK,IAAI,CAAC,gBAAgB;gBACzD,eAAe,KAAK,IAAI,CAAC,qBAAqB,GAAG,4BAA4B;gBAC7E,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,gBAAgB;YACnD,OAAO;gBACL,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,KAAK,OAAO,IAAI,iBAAiB;YACtE;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;QACF,SAAU;YACR,0BAA0B;QAC5B;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,SAAS;QAAqB,UAAU;kBAC7C,yBAAyB,qCAAqC;;;;;;AAGrE;uCAEe", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/app/actions/stripeActions.ts"], "sourcesContent": ["// src/app/actions/stripeActions.ts\r\n'use server';\r\n\r\nimport { stripe } from '@/lib/stripe/server'; // Your server-side Stripe client\r\nimport { createSupabaseServerClient } from '@/lib/supabase/server'; // For getting user session\r\nimport { headers } from 'next/headers';\r\nimport { redirect } from 'next/navigation';\r\nimport <PERSON><PERSON> from 'stripe';\r\n\r\n// Helper function to get base URL for redirect URLs\r\nasync function getURL(path: string = '') { // Make the function async\r\n  const headersInstance = await headers(); // Await the headers() result\r\n  const host = headersInstance.get('host'); // Access get() on the awaited instance\r\n  const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\r\n  return `${protocol}://${host}${path}`;\r\n}\r\n\r\ninterface CreateCheckoutSessionArgs {\r\n  priceId: string;\r\n  // You might add quantity, metadata, etc. later\r\n}\r\n\r\nexport async function createCheckoutSession(args: CreateCheckoutSessionArgs) {\r\n  const { priceId } = args;\r\n  const supabase = await createSupabaseServerClient(); // Use await here\r\n\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    console.error('User not authenticated for creating checkout session:', userError);\r\n    // Could return an error object or redirect to login\r\n    // For simplicity, let's assume this action is called from an authenticated context\r\n    // and a proper redirect would happen at the page level if no user.\r\n    // However, redirecting here is also an option:\r\n    // return redirect('/login?message=Authentication required');\r\n    return { error: 'User not authenticated.' };\r\n  }\r\n\r\n  // Optional: Check if user already has an active subscription to this or another plan\r\n  // This logic would involve querying your `subscriptions` table in Supabase.\r\n  // For Phase 0, we might skip this complex check and let Stripe handle\r\n  // if a customer tries to subscribe to the same plan twice (it usually prevents it).\r\n\r\n  // Optional: Get or create Stripe Customer ID and store it on the user's profile\r\n  // This is best practice for linking Supabase users to Stripe customers.\r\n  let stripeCustomerId: string | undefined;\r\n\r\n  const { data: profile, error: profileError } = await supabase\r\n    .from('profiles')\r\n    .select('stripe_customer_id')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError && profileError.code !== 'PGRST116') { // PGRST116: row not found\r\n    console.error('Error fetching profile for Stripe customer ID:', profileError);\r\n    return { error: 'Could not retrieve user profile.' };\r\n  }\r\n\r\n  stripeCustomerId = profile?.stripe_customer_id ?? undefined;\r\n\r\n  if (!stripeCustomerId) {\r\n    try {\r\n      const customer = await stripe.customers.create({\r\n        email: user.email,\r\n        metadata: {\r\n          supabaseUserId: user.id,\r\n      },\r\n    } as Stripe.Checkout.SessionCreateParams);\r\n      stripeCustomerId = customer.id;\r\n      // Save the new Stripe Customer ID to the user's profile in Supabase\r\n      const { error: updateProfileError } = await supabase\r\n        .from('profiles')\r\n        .update({ stripe_customer_id: stripeCustomerId })\r\n        .eq('id', user.id);\r\n\r\n      if (updateProfileError) {\r\n        console.error('Error updating profile with Stripe Customer ID:', updateProfileError);\r\n        // Non-critical for checkout creation itself, but log it.\r\n      }\r\n    } catch (e: any) {\r\n        console.error('Error creating Stripe customer:', e);\r\n        return { error: `Could not create Stripe customer: ${e.message}` };\r\n    }\r\n  }\r\n\r\n  try {\r\n    const successUrl = await getURL('/dashboard?checkout_session_id={CHECKOUT_SESSION_ID}&status=success');\r\n    const cancelUrl = await getURL('/subscriptions?status=cancelled');\r\n\r\n    const session = await stripe.checkout.sessions.create({\r\n      payment_method_types: ['card'],\r\n      mode: 'subscription', // For one-time payments, use 'payment'\r\n      customer: stripeCustomerId, // Use existing or newly created Stripe Customer ID\r\n      line_items: [\r\n        {\r\n          price: priceId,\r\n          quantity: 1,\r\n        },\r\n      ],\r\n      // Define success and cancel URLs\r\n      // These are URLs Stripe will redirect the user to after payment.\r\n      success_url: await getURL('/dashboard?checkout_session_id={CHECKOUT_SESSION_ID}&status=success'), // Or a dedicated success page\r\n      cancel_url: await getURL('/subscriptions?status=cancelled'),\r\n      // Optional: Pass metadata to Stripe session, useful for webhooks\r\n      metadata: {\r\n        supabaseUserId: user.id,\r\n        priceId: priceId,\r\n        // any other info you want to track\r\n      },\r\n    });\r\n\r\n    if (session.url) {\r\n      // If using this server action from a client component form,\r\n      // you might want to return the session.url or session.id\r\n      // and let the client handle the redirect.\r\n      // For a direct form submission to a server action, redirecting here is fine.\r\n      // return redirect(session.url); // This won't work directly if action is called via fetch from client\r\n      return { sessionId: session.id, url: session.url, error: null };\r\n    } else {\r\n      return { error: 'Failed to create Stripe session.' };\r\n    }\r\n\r\n  } catch (e: any) {\r\n    console.error('Error creating Stripe checkout session:', e);\r\n    return { error: `Could not create checkout session: ${e.message}` };\r\n  }\r\n}\r\n\r\nexport async function cancelSubscriptionAction(stripeSubscriptionId: string): Promise<{ error?: string; success?: string }> {\r\n  'use server'; // Ensure this is treated as a server action\r\n\r\n  const supabase = await createSupabaseServerClient();\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    console.error('User not authenticated for cancelling subscription:', userError);\r\n    return { error: 'User not authenticated. Please log in again.' };\r\n  }\r\n\r\n  if (!stripeSubscriptionId) {\r\n    console.error('Stripe Subscription ID is required to cancel.');\r\n    return { error: 'Subscription ID is missing.' };\r\n  }\r\n\r\n  try {\r\n    // Optional: Verify that the subscription belongs to the user\r\n    // This would involve fetching the subscription from your DB and checking user_id\r\n    // For now, we assume the ID passed is correct and belongs to the user if they see the button.\r\n\r\n    const updatedSubscription = await stripe.subscriptions.update(\r\n      stripeSubscriptionId,\r\n      { cancel_at_period_end: true }\r\n    );\r\n\r\n    if (updatedSubscription.cancel_at_period_end) {\r\n      // Optionally, you might want to immediately update your local database here\r\n      // to reflect that cancellation is pending, though the webhook will also handle this.\r\n      // For example:\r\n      // await supabase\r\n      //   .from('subscriptions')\r\n      //   .update({ status: 'pending_cancellation', cancel_at_period_end: true, /* or a specific cancel_at timestamp */ })\r\n      //   .eq('stripe_subscription_id', stripeSubscriptionId)\r\n      //   .eq('user_id', user.id);\r\n\r\n      return { success: 'Subscription cancellation requested. It will be cancelled at the end of the current billing period.' };\r\n    } else {\r\n      console.error('Stripe did not confirm cancel_at_period_end for subscription:', stripeSubscriptionId, updatedSubscription);\r\n      return { error: 'Failed to request subscription cancellation from Stripe. Please try again.' };\r\n    }\r\n\r\n  } catch (e: any) {\r\n    console.error('Error cancelling Stripe subscription:', e);\r\n    // More specific error handling based on Stripe error codes could be added here\r\n    if (e.code === 'resource_missing') {\r\n        return { error: `Could not find subscription with ID: ${stripeSubscriptionId}. It might have been already cancelled or deleted.` };\r\n    }\r\n    return { error: `Could not cancel subscription: ${e.message}` };\r\n  }\r\n}\r\n\r\nexport async function createStripePortalSession(): Promise<{ error?: string; url?: string }> {\r\n  'use server';\r\n\r\n  const supabase = await createSupabaseServerClient();\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    console.error('User not authenticated for creating Stripe portal session:', userError);\r\n    return { error: 'User not authenticated. Please log in again.' };\r\n  }\r\n\r\n  let stripeCustomerId: string | undefined;\r\n\r\n  const { data: profile, error: profileError } = await supabase\r\n    .from('profiles')\r\n    .select('stripe_customer_id')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError && profileError.code !== 'PGRST116') { // PGRST116: row not found\r\n    console.error('Error fetching profile for Stripe customer ID:', profileError);\r\n    return { error: 'Could not retrieve user profile.' };\r\n  }\r\n\r\n  stripeCustomerId = profile?.stripe_customer_id ?? undefined;\r\n\r\n  if (!stripeCustomerId) {\r\n    console.error('Stripe Customer ID not found for user:', user.id);\r\n    // This could happen if the webhook didn't run or failed to update the profile\r\n    // Or if the user somehow skipped the checkout process that creates a customer.\r\n    // For a portal, a customer ID is essential.\r\n    return { error: 'Stripe customer information not found. Please complete a subscription first or contact support.' };\r\n  }\r\n\r\n  try {\r\n    const returnUrl = await getURL('/dashboard'); // Return to the dashboard\r\n\r\n    const portalSession = await stripe.billingPortal.sessions.create({\r\n      customer: stripeCustomerId,\r\n      return_url: returnUrl,\r\n    });\r\n\r\n    if (portalSession.url) {\r\n      return { url: portalSession.url };\r\n    } else {\r\n      console.error('Failed to create Stripe portal session: No URL returned');\r\n      return { error: 'Failed to create customer portal session.' };\r\n    }\r\n  } catch (e: any) {\r\n    console.error('Error creating Stripe portal session:', e);\r\n    return { error: `Could not create customer portal session: ${e.message}` };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAsBsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/subscriptions/SubscribeButton.tsx"], "sourcesContent": ["// src/components/subscriptions/SubscribeButton.tsx\r\n'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Button } from '@/components/ui/button'; // Adjust import path if needed\r\nimport { createCheckoutSession } from '@/app/actions/stripeActions'; // Adjust import path if needed\r\nimport { useRouter } from 'next/navigation'; // For client-side redirect\r\nimport { useToast } from '@/components/ui/use-toast'; // Assuming you use shadcn/ui toast\r\n\r\ninterface SubscribeButtonProps {\r\n  priceId: string;\r\n  planName: string;\r\n}\r\n\r\nexport function SubscribeButton({ priceId, planName }: SubscribeButtonProps) {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const router = useRouter();\r\n  const { toast } = useToast();\r\n\r\n  const handleSubscribe = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const result = await createCheckoutSession({ priceId });\r\n\r\n      if (result.error) {\r\n        console.error('Error creating checkout session:', result.error);\r\n        toast({\r\n          title: \"Subscription Error\",\r\n          description: result.error,\r\n          variant: \"destructive\",\r\n        });\r\n      } else if (result.url) {\r\n        // Redirect to Stripe Checkout\r\n        router.push(result.url);\r\n        // No need to setIsLoading(false) as we are navigating away\r\n        return;\r\n      } else {\r\n         toast({\r\n          title: \"Subscription Error\",\r\n          description: \"Could not initiate subscription. Please try again.\",\r\n          variant: \"destructive\",\r\n        });\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Unexpected error during subscription:', error);\r\n      toast({\r\n        title: \"Subscription Error\",\r\n        description: error.message || \"An unexpected error occurred.\",\r\n        variant: \"destructive\",\r\n      });\r\n    }\r\n    setIsLoading(false);\r\n  };\r\n\r\n  return (\r\n    <Button onClick={handleSubscribe} disabled={isLoading} className=\"w-full\">\r\n      {isLoading ? 'Processing...' : `Subscribe to ${planName}`}\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;AAGnD;AACA,mOAAiD,+BAA+B;AAChF,+QAAqE,+BAA+B;AACpG,sOAA6C,2BAA2B;AACxE,0OAAsD,mCAAmC;AANzF;;;;;;;AAaO,SAAS,gBAAgB,EAAE,OAAO,EAAE,QAAQ,EAAwB;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,wBAAqB,AAAD,EAAE;gBAAE;YAAQ;YAErD,IAAI,OAAO,KAAK,EAAE;gBAChB,QAAQ,KAAK,CAAC,oCAAoC,OAAO,KAAK;gBAC9D,MAAM;oBACJ,OAAO;oBACP,aAAa,OAAO,KAAK;oBACzB,SAAS;gBACX;YACF,OAAO,IAAI,OAAO,GAAG,EAAE;gBACrB,8BAA8B;gBAC9B,OAAO,IAAI,CAAC,OAAO,GAAG;gBACtB,2DAA2D;gBAC3D;YACF,OAAO;gBACJ,MAAM;oBACL,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;YACX;QACF;QACA,aAAa;IACf;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,SAAS;QAAiB,UAAU;QAAW,WAAU;kBAC9D,YAAY,kBAAkB,CAAC,aAAa,EAAE,UAAU;;;;;;AAG/D", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/RealtimeTransaction.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { createSupabaseBrowserClient } from '@/lib/supabase/client'; // Use the client-side Supabase client\r\nimport { Database } from '@/types/supabase'; // Replace with your actual types\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { cn } from \"@/lib/utils\"; // Assuming cn utility is available\r\nimport { Loader2 } from \"lucide-react\"; // Assuming lucide-react is installed\r\n\r\ninterface RealtimeTransactionProps {\r\n  orderId: string;\r\n  userId: string;\r\n}\r\n\r\nconst RealtimeTransaction: React.FC<RealtimeTransactionProps> = ({ orderId, userId }) => {\r\n  const [transactionStatus, setTransactionStatus] = useState<string | null>(null);\r\n  const supabase = createSupabaseBrowserClient();\r\n\r\n  useEffect(() => {\r\n    // Only subscribe if orderId is provided\r\n    if (!orderId) {\r\n      return;\r\n    }\r\n\r\n    const channel = supabase\r\n      .channel(`eloh_crypto_transactions_${orderId}`)\r\n      .on(\r\n        'postgres_changes',\r\n        {\r\n          event: 'UPDATE',\r\n          schema: 'public',\r\n          table: 'eloh_crypto_transactions',\r\n          filter: `quikrsolutions_order_id=eq.${orderId}`,\r\n        },\r\n        (payload: { new: Database['public']['Tables']['eloh_crypto_transactions']['Row'] }) => {\r\n          console.log('Received Realtime update:', payload);\r\n          const newStatus = payload.new.status;\r\n          setTransactionStatus(newStatus);\r\n        }\r\n      )\r\n      .subscribe();\r\n\r\n    return () => {\r\n      supabase.removeChannel(channel);\r\n    };\r\n  }, [orderId, supabase]);\r\n\r\n  const statusBadge = () => {\r\n    switch (transactionStatus) {\r\n      case \"pending_payment\":\r\n        return <Badge variant=\"secondary\">Pending Payment</Badge>;\r\n      case \"processing\":\r\n        return (\r\n          <Badge variant=\"outline\">\r\n            Processing <Loader2 className=\"ml-2 h-4 w-4 animate-spin\" />\r\n          </Badge>\r\n        );\r\n      case \"confirmed\":\r\n        return <Badge variant=\"default\" className=\"bg-green-500 hover:bg-green-500\">Confirmed</Badge>; // Using default and adding green background\r\n      case \"invoice_failed_or_expired\":\r\n        return <Badge variant=\"destructive\">Failed/Expired</Badge>;\r\n      default:\r\n        return <Badge>{transactionStatus || 'Unknown Status'}</Badge>;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h2 className=\"text-lg font-semibold mb-2\">Transaction Details</h2>\r\n      <p className=\"mb-1\">Order ID: {orderId}</p>\r\n      <div className=\"flex items-center mb-4\">\r\n        <p className=\"mr-2\">Status:</p>\r\n        {statusBadge()}\r\n      </div>\r\n      {/* You might want to fetch and display more transaction details here if needed */}\r\n      {/* For now, we focus on the status update */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RealtimeTransaction;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,+NAAqE,sCAAsC;AAE3G;AAEA,2WAAwC,qCAAqC;AAP7E;;;;;;AAcA,MAAM,sBAA0D,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE;IAClF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,IAAI,CAAC,SAAS;YACZ;QACF;QAEA,MAAM,UAAU,SACb,OAAO,CAAC,CAAC,yBAAyB,EAAE,SAAS,EAC7C,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,2BAA2B,EAAE,SAAS;QACjD,GACA,CAAC;YACC,QAAQ,GAAG,CAAC,6BAA6B;YACzC,MAAM,YAAY,QAAQ,GAAG,CAAC,MAAM;YACpC,qBAAqB;QACvB,GAED,SAAS;QAEZ,OAAO;YACL,SAAS,aAAa,CAAC;QACzB;IACF,GAAG;QAAC;QAAS;KAAS;IAEtB,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;YACpC,KAAK;gBACH,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;;wBAAU;sCACZ,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;YAGpC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAkC;;;;;0BAAmB,4CAA4C;YAC7I,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;8BAAE,qBAAqB;;;;;;QACxC;IACF;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,8OAAC;gBAAE,WAAU;;oBAAO;oBAAW;;;;;;;0BAC/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAO;;;;;;oBACnB;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/app/%28app%29/subscriptions/SubscriptionClientContent.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';\r\nimport PayWithCryptoButton from '@/components/subscriptions/PayWithCryptoButton';\r\nimport { SubscribeButton } from '@/components/subscriptions/SubscribeButton';\r\nimport RealtimeTransaction from '@/components/RealtimeTransaction';\r\nimport { Database } from '@/types/supabase'; // Import your database types\r\n\r\ninterface SubscriptionClientContentProps {\r\n  user: Database['public']['Tables']['profiles']['Row']; // Assuming user type from profiles table\r\n  plans: { name: string; price: string; priceId: string; description: string }[];\r\n}\r\n\r\nconst SubscriptionClientContent: React.FC<SubscriptionClientContentProps> = ({ user, plans }) => {\r\n  const [orderId, setOrderId] = useState<string | null>(null);\r\n\r\n  return (\r\n    <div className=\"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\r\n      {plans.map((plan) => (\r\n        <Card key={plan.priceId} style={{ minHeight: '200px' }}>\r\n          <CardHeader>\r\n            <CardTitle>{plan.name}</CardTitle>\r\n            <CardDescription>{plan.price}</CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <p>{plan.description}</p>\r\n          </CardContent>\r\n          <CardFooter style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\r\n            <SubscribeButton priceId={plan.priceId} planName={plan.name + \" (Credit/Debit Card)\"} />\r\n            <br />\r\n            <div>\r\n              <PayWithCryptoButton\r\n                amount={parseFloat(plan.price.replace(\"$\", \"\").replace(\"/month\", \"\"))}\r\n                currency={\"USD\"}\r\n                planId={plan.priceId}\r\n                userId={user.id}\r\n                onOrderCreated={(id) => {\r\n                  console.log(\"Order ID created:\", id);\r\n                  setOrderId(id);\r\n                }}\r\n              />\r\n              {/* Render RealtimeTransaction only if orderId is available */}\r\n              {orderId && <RealtimeTransaction orderId={orderId} userId={user.id} />}\r\n            </div>\r\n          </CardFooter>\r\n        </Card>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SubscriptionClientContent;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAcA,MAAM,4BAAsE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;IAC1F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,gIAAA,CAAA,OAAI;gBAAoB,OAAO;oBAAE,WAAW;gBAAQ;;kCACnD,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAE,KAAK,IAAI;;;;;;0CACrB,8OAAC,gIAAA,CAAA,kBAAe;0CAAE,KAAK,KAAK;;;;;;;;;;;;kCAE9B,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;sCAAG,KAAK,WAAW;;;;;;;;;;;kCAEtB,8OAAC,gIAAA,CAAA,aAAU;wBAAC,OAAO;4BAAE,SAAS;4BAAQ,eAAe;4BAAU,YAAY;wBAAS;;0CAClF,8OAAC,sJAAA,CAAA,kBAAe;gCAAC,SAAS,KAAK,OAAO;gCAAE,UAAU,KAAK,IAAI,GAAG;;;;;;0CAC9D,8OAAC;;;;;0CACD,8OAAC;;kDACC,8OAAC,0JAAA,CAAA,UAAmB;wCAClB,QAAQ,WAAW,KAAK,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,UAAU;wCACjE,UAAU;wCACV,QAAQ,KAAK,OAAO;wCACpB,QAAQ,KAAK,EAAE;wCACf,gBAAgB,CAAC;4CACf,QAAQ,GAAG,CAAC,qBAAqB;4CACjC,WAAW;wCACb;;;;;;oCAGD,yBAAW,8OAAC,yIAAA,CAAA,UAAmB;wCAAC,SAAS;wCAAS,QAAQ,KAAK,EAAE;;;;;;;;;;;;;;;;;;;eAvB7D,KAAK,OAAO;;;;;;;;;;AA8B/B;uCAEe", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAAG;;AACJ,MAAM,gBAAgB,CAAC,QAAQ,OAAO,UAAU,YAAY,GAAG,OAAO,GAAG,UAAU,IAAI,MAAM;AACtF,MAAM,KAAK,qIAAA,CAAA,OAAI;AACf,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC;QAC5B,IAAI;QACJ,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;QACvN,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QACtC,MAAM,uBAAuB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpD,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ;YAChF,MAAM,qBAAqB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,QAAQ;YACrH,IAAI,gBAAgB,MAAM,OAAO;YACjC,MAAM,aAAa,cAAc,gBAAgB,cAAc;YAC/D,OAAO,QAAQ,CAAC,QAAQ,CAAC,WAAW;QACxC;QACA,MAAM,wBAAwB,SAAS,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACtE,IAAI,CAAC,KAAK,MAAM,GAAG;YACnB,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACX,GAAG,CAAC;QACJ,MAAM,+BAA+B,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,MAAM,QAAQ,6BAA6B,KAAK,IAAI,KAAK,IAAI,yBAAyB,MAAM,CAAC,CAAC,KAAK;YACvO,IAAI,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,wBAAwB,GAAG;YAC5E,OAAO,OAAO,OAAO,CAAC,wBAAwB,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,CAAC;oBACzC,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,IAAI,CAAC;oBACP,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,KAAK;mBACE;gBACH;gBACA;aACH,GAAG;QACR,GAAG,EAAE;QACL,OAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAChM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 866, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-client-wrapper.ts"], "sourcesContent": ["// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nexport { callServer } from 'next/dist/client/app-call-server'\nexport { findSourceMapURL } from 'next/dist/client/app-find-source-map-url'\n\n// A noop wrapper to let the Flight client create the server reference.\n// See also: https://github.com/facebook/react/pull/26632\n// Since we're using the Edge build of Flight client for SSR [1], here we need to\n// also use the same Edge build to create the reference. For the client bundle,\n// we use the default and let Webpack to resolve it to the correct version.\n// 1: https://github.com/vercel/next.js/blob/16eb80b0b0be13f04a6407943664b5efd8f3d7d0/packages/next/src/server/app-render/use-flight-response.tsx#L24-L26\nexport const createServerReference = (\n  (!!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')) as typeof import('react-server-dom-webpack/client')\n).createServerReference\n"], "names": ["callServer", "createServerReference", "findSourceMapURL", "process", "env", "NEXT_RUNTIME", "require"], "mappings": "AAAA,gFAAgF;AAChF,0BAA0B;;;;;;;;;;;;;;;;;IAEjBA,UAAU,EAAA;eAAVA,eAAAA,UAAU;;IASNC,qBAAqB,EAAA;eAArBA;;IARJC,gBAAgB,EAAA;eAAhBA,qBAAAA,gBAAgB;;;+BADE;qCACM;AAQ1B,MAAMD,wBACV,CAAA,CAAC,CAACE,QAAQC,GAAG,CAACC,YAAY,GAEvBC,QAAQ,0CAERA,QAAQ,8JAAiC,EAC7CL,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}