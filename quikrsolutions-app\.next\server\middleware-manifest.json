{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1531e49d._.js", "server/edge/chunks/[root-of-the-server]__004c5692._.js", "server/edge/chunks/edge-wrapper_733c3edd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z9PPNSj/YWFrw5ioAWDsPog7dEpHBvFszhaZqUDq7zI=", "__NEXT_PREVIEW_MODE_ID": "fd9c7248c0f54c5710070ce94b578ca5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "532daa5612d5efdf813db82aaa39e18ff8f0328f03211ad7baefa7728eb736ea", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9a39f5833b2ad05cb72b08ab0eeb95ce82153eeb89a7415c72430e33bc13f753"}}}, "sortedMiddleware": ["/"], "functions": {}}