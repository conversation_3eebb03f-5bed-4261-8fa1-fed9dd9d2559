{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1531e49d._.js", "server/edge/chunks/[root-of-the-server]__004c5692._.js", "server/edge/chunks/edge-wrapper_733c3edd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Q/miLRP/eFb12tAkKsBxm0kUy5YKDRqikWQ9itnXmXo=", "__NEXT_PREVIEW_MODE_ID": "ea4f1e885d89a6b41958a407883f48aa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1badee74fd4e882a0d2051078381631922687c2c28a785a5a4bebd59f74153d7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0c09184b4872796772d67503e806416f8de5312f07626a8c0fe2047d62a8304a"}}}, "sortedMiddleware": ["/"], "functions": {}}