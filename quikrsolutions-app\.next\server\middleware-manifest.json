{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1531e49d._.js", "server/edge/chunks/[root-of-the-server]__004c5692._.js", "server/edge/chunks/edge-wrapper_733c3edd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lRloOQJaAbuaUGGBdgIKM55VfpwjYmr+hMgdrlSCSBk=", "__NEXT_PREVIEW_MODE_ID": "a73e0942297bf6a7fa61c56941045d3b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6f00ff5e931756299963f9c2b574ead3a4ce74c34599e8b9d5e9478b36f81bf3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e79b4577dc2fe94e1db5512db17c3fffbf925d05964486b5e58aa61666e8c60d"}}}, "sortedMiddleware": ["/"], "functions": {}}