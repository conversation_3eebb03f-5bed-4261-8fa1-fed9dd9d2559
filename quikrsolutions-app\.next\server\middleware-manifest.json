{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1531e49d._.js", "server/edge/chunks/[root-of-the-server]__004c5692._.js", "server/edge/chunks/edge-wrapper_733c3edd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z9PPNSj/YWFrw5ioAWDsPog7dEpHBvFszhaZqUDq7zI=", "__NEXT_PREVIEW_MODE_ID": "dd91458626934d45779a017dda6fc237", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8a8891679d4b4746104463008559bf6a90651b2434d32b5b40b16a2c2ad950a6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "abba71f97cc18f494ed4969fab0cc9086ff0a805b5415a1f72ba848596dfc4ed"}}}, "sortedMiddleware": ["/"], "functions": {}}