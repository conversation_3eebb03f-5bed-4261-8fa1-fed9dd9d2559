{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1531e49d._.js", "server/edge/chunks/[root-of-the-server]__004c5692._.js", "server/edge/chunks/edge-wrapper_733c3edd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z9PPNSj/YWFrw5ioAWDsPog7dEpHBvFszhaZqUDq7zI=", "__NEXT_PREVIEW_MODE_ID": "72b93efc69b5e96b5a914ffbf3e7d3f7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9d1fc006409969bfb2941ac0dd7eb8d8594d8601eb8f7ac339e5dd33e0b73da2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c02a324c3458716c52a7260130c4876d0e832d7c944eaf1411b909c1b8edd89a"}}}, "sortedMiddleware": ["/"], "functions": {}}