{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1531e49d._.js", "server/edge/chunks/[root-of-the-server]__004c5692._.js", "server/edge/chunks/edge-wrapper_733c3edd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lRloOQJaAbuaUGGBdgIKM55VfpwjYmr+hMgdrlSCSBk=", "__NEXT_PREVIEW_MODE_ID": "6d2875ea0de9c2b24f1859a8812d4dde", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6a4cd80fcf7c32f207e1448d927c1a2a21376a0fa4e38c665492df1c1dfdac94", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "81560a93c6501ba4bf4750ceca868cff0b38e280dd4aa13f550a4f91ee8046d2"}}}, "sortedMiddleware": ["/"], "functions": {}}