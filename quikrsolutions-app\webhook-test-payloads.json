{"processing_status": {"description": "Remittance status changed to processing", "payload": {"event_type": "remittance.status_updated", "data": {"eloh_remittance_id": "eloh_rem_123456789", "quikrsolutions_remittance_id": "qr_rem_987654321", "status": "processing", "previous_status": "pending", "updated_at": "2024-01-15T10:30:00Z", "details": {"message": "Remittance is now being processed by our partner network"}}, "timestamp": "2024-01-15T10:30:00Z"}}, "in_transit_status": {"description": "Remittance is in transit to recipient", "payload": {"event_type": "remittance.status_updated", "data": {"eloh_remittance_id": "eloh_rem_123456789", "quikrsolutions_remittance_id": "qr_rem_987654321", "status": "in_transit", "previous_status": "processing", "updated_at": "2024-01-15T12:15:00Z", "details": {"message": "Funds are in transit to the recipient location", "estimated_arrival": "2024-01-15T16:00:00Z"}}, "timestamp": "2024-01-15T12:15:00Z"}}, "ready_for_pickup_status": {"description": "Remittance ready for pickup at destination", "payload": {"event_type": "remittance.status_updated", "data": {"eloh_remittance_id": "eloh_rem_123456789", "quikrsolutions_remittance_id": "qr_rem_987654321", "status": "ready_for_pickup", "previous_status": "in_transit", "updated_at": "2024-01-15T15:45:00Z", "details": {"message": "Funds are ready for pickup at the designated location", "pickup_location": "MoneyGram Agent - Downtown Branch", "pickup_reference": "REF123456", "pickup_instructions": "Bring valid ID and reference number"}}, "timestamp": "2024-01-15T15:45:00Z"}}, "completed_status": {"description": "Remittance successfully completed", "payload": {"event_type": "remittance.status_updated", "data": {"eloh_remittance_id": "eloh_rem_123456789", "quikrsolutions_remittance_id": "qr_rem_987654321", "status": "completed", "previous_status": "ready_for_pickup", "updated_at": "2024-01-15T18:20:00Z", "details": {"message": "Remittance has been successfully completed", "completion_method": "cash_pickup", "completed_at": "2024-01-15T18:20:00Z"}}, "timestamp": "2024-01-15T18:20:00Z"}}, "failed_status": {"description": "Remittance failed due to compliance issue", "payload": {"event_type": "remittance.status_updated", "data": {"eloh_remittance_id": "eloh_rem_123456789", "quikrsolutions_remittance_id": "qr_rem_987654321", "status": "failed", "previous_status": "processing", "updated_at": "2024-01-15T11:00:00Z", "details": {"message": "Remittance failed due to compliance requirements", "failure_reason": "recipient_verification_failed", "error_code": "COMPLIANCE_001", "next_steps": "Please contact customer support for assistance"}}, "timestamp": "2024-01-15T11:00:00Z"}}}