import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Heart, MessageCircle, Share2, Star } from 'lucide-react'
import { Button } from '@/components/ui/button'

const FeedPage = () => {
  // Placeholder feed items representing diverse content
  const feedItems = [
    {
      id: 1,
      type: 'product',
      title: 'Premium Wireless Headphones',
      description: 'High-quality noise-canceling headphones with 30-hour battery life.',
      price: '$299.99',
      rating: 4.8,
      category: 'Electronics',
      image: '/placeholder-product.jpg'
    },
    {
      id: 2,
      type: 'service',
      title: 'Professional Web Development',
      description: 'Custom website development with modern frameworks and responsive design.',
      price: 'From $1,500',
      rating: 4.9,
      category: 'Services',
      image: '/placeholder-service.jpg'
    },
    {
      id: 3,
      type: 'food',
      title: 'Gourmet Pizza Special',
      description: 'Authentic Italian pizza with fresh ingredients. Limited time offer!',
      price: '$18.99',
      rating: 4.7,
      category: 'Food',
      image: '/placeholder-food.jpg'
    },
    {
      id: 4,
      type: 'rental',
      title: 'Luxury Apartment Downtown',
      description: 'Modern 2-bedroom apartment in the heart of the city. Fully furnished.',
      price: '$2,500/month',
      rating: 4.6,
      category: 'Rentals',
      image: '/placeholder-rental.jpg'
    },
    {
      id: 5,
      type: 'job',
      title: 'Senior React Developer',
      description: 'Join our innovative team building next-generation web applications.',
      price: '$80k - $120k',
      rating: null,
      category: 'Jobs',
      image: '/placeholder-job.jpg'
    }
  ]

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Welcome to Quikrsolutions</h1>
        <p className="text-muted-foreground">
          Discover products, services, food, rentals, jobs, and more in your all-in-one digital ecosystem
        </p>
      </div>

      <div className="space-y-6">
        {feedItems.map((item) => (
          <Card key={item.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{item.category}</Badge>
                    <Badge variant="outline" className="capitalize">{item.type}</Badge>
                  </div>
                  <CardTitle className="text-xl">{item.title}</CardTitle>
                  <CardDescription>{item.description}</CardDescription>
                </div>
                <div className="text-right">
                  <p className="text-lg font-semibold text-primary">{item.price}</p>
                  {item.rating && (
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm text-muted-foreground">{item.rating}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Button variant="ghost" size="sm">
                    <Heart className="h-4 w-4 mr-1" />
                    Like
                  </Button>
                  <Button variant="ghost" size="sm">
                    <MessageCircle className="h-4 w-4 mr-1" />
                    Comment
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                </div>
                <Button size="sm">
                  {item.type === 'job' ? 'Apply Now' :
                   item.type === 'rental' ? 'View Details' :
                   item.type === 'service' ? 'Get Quote' : 'View Item'}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center py-8">
        <p className="text-muted-foreground mb-4">
          This is a preview of your personalized feed. More content and features coming soon!
        </p>
        <Button variant="outline">Load More Content</Button>
      </div>
    </div>
  )
}

export default FeedPage