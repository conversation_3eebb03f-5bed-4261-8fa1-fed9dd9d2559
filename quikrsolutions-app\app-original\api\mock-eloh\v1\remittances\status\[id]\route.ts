// src/app/api/mock-eloh/v1/remittances/status/[id]/route.ts

import { NextResponse } from 'next/server';
import { RemittanceStatusResponse, ElohErrorResponse } from '@/lib/services/eloh/elohTypes';

// In a real mock, you might store remittance statuses in memory or a file
// and update them based on simulated webhooks.
// For this basic mock, we'll return a static status or simulate transitions.

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
): Promise<NextResponse<RemittanceStatusResponse | ElohErrorResponse>> {
  try {
    const elohRemittanceId = params.id;

    if (!elohRemittanceId) {
      return NextResponse.json(
        { status: 'error', message: 'Missing remittance ID' },
        { status: 400 }
      );
    }

    // Simulate fetching remittance status
    // This is a very basic mock. A more advanced mock would track status changes.
    const mockStatus: string = 'completed'; // Always return completed for simplicity

    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const successResponse: RemittanceStatusResponse = {
      status: 'success',
      data: {
        elohRemittanceId: elohRemittanceId,
        quikrsolutionsRemittanceId: `qs_rem_${elohRemittanceId.split('_').pop()}`, // Simulate mapping back to QS ID
        currentStatus: mockStatus,
        statusHistory: [
          { status: 'pending', timestamp: new Date(Date.now() - 10000).toISOString() },
          { status: 'processing', timestamp: new Date(Date.now() - 5000).toISOString() },
          { status: 'completed', timestamp: new Date().toISOString() },
        ],
    // Add other relevant mock data
    stellarTransactionId: `stellar_tx_${elohRemittanceId.split('_').pop()}`,
    pickupCode: mockStatus === 'ready_for_pickup' ? 'ABC123' : undefined, // Only include pickupCode if status is 'ready_for_pickup'
  },
};

    return NextResponse.json(successResponse, { status: 200 });

  } catch (error: any) {
    console.error('Error in mock remittance status endpoint:', error);
    const errorResponse: ElohErrorResponse = {
      status: 'error',
      message: 'Internal mock server error',
      details: {
        error: error.message,
      },
    };
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
