# Remittance Flow End-to-End Testing Guide

## Overview
This guide provides step-by-step instructions for testing the complete remittance flow in Quikrsolutions.app, including form submission, database updates, webhook simulation, and real-time UI updates.

## Prerequisites
1. Development server running (`npm run dev`)
2. Supabase database accessible
3. Node.js 18+ for webhook simulation
4. Valid authentication session

## Testing Flow

### Phase 1: Initial Setup and Form Submission

#### Step 1: Access New Remittance Form
1. Navigate to `http://localhost:3001/app/remittances/new`
2. Verify form loads with all required fields
3. Check that exchange rate fetching works when amount and currencies are selected

#### Step 2: Submit New Remittance
1. Fill out the form with test data:
   - **Amount**: 100
   - **Send Currency**: USD
   - **Receive Currency**: XCD
   - **Recipient First Name**: John
   - **Recipient Last Name**: Doe
   - **Recipient Country**: Dominica
   - **Recipient Phone**: +1767123456
   - **Recipient Email**: <EMAIL>
   - **Purpose**: Family Support

2. Wait for exchange rate quote to load
3. Click "Initiate Remittance"
4. Verify success message and redirect to history page

#### Step 3: Verify Database Insert
1. Check Supabase `remittance_transactions` table
2. Confirm new record with status "pending"
3. Note the `quikrsolutions_remittance_id` for testing

### Phase 2: Real-time Updates Testing

#### Step 4: Access Tracking Pages
1. **History Page**: `http://localhost:3001/app/remittances`
   - Verify remittance appears in table
   - Check status badge shows "pending"
   
2. **Tracking Page**: `http://localhost:3001/app/remittances/track?id=YOUR_REMITTANCE_ID`
   - Verify remittance details display correctly
   - Check real-time indicator is present

#### Step 5: Simulate Webhook Status Updates
Use the provided webhook simulation script to test status changes:

```bash
# Make script executable (Unix/Mac)
chmod +x simulate_webhook.js

# Test processing status
node simulate_webhook.js processing YOUR_REMITTANCE_ID

# Test in-transit status
node simulate_webhook.js in_transit YOUR_REMITTANCE_ID

# Test ready for pickup
node simulate_webhook.js ready_for_pickup YOUR_REMITTANCE_ID

# Test completion
node simulate_webhook.js completed YOUR_REMITTANCE_ID
```

#### Step 6: Verify Real-time Updates
After each webhook simulation:
1. **Check Database**: Verify status updated in Supabase
2. **Check History Page**: Status badge should update automatically
3. **Check Tracking Page**: Status and timeline should update in real-time
4. **Check Browser Console**: No errors should appear

### Phase 3: Error Handling Testing

#### Step 7: Test Failed Remittance
```bash
node simulate_webhook.js failed YOUR_REMITTANCE_ID
```
1. Verify status changes to "failed"
2. Check error details display properly
3. Confirm UI handles failed state correctly

#### Step 8: Test Invalid Tracking ID
1. Navigate to tracking page with invalid ID
2. Verify proper error message displays
3. Test manual ID entry with invalid ID

## Expected Results

### Successful Flow Indicators
- ✅ Form submission without errors
- ✅ Database record created with correct data
- ✅ Real-time status updates work
- ✅ UI updates without page refresh
- ✅ Proper error handling for invalid states

### Database State Verification
Check these fields update correctly:
- `status`: pending → processing → in_transit → ready_for_pickup → completed
- `updated_at`: Updates with each webhook
- `eloh_remittance_id`: Set during initial creation

### UI State Verification
- Status badges change colors appropriately
- Timeline shows progression
- Real-time indicator appears during updates
- Loading states work properly

## Troubleshooting

### Common Issues
1. **Webhook not received**: Check webhook URL and signature
2. **Real-time not working**: Verify Supabase Realtime is enabled
3. **Form submission fails**: Check API route logs
4. **Database not updating**: Verify webhook handler logic

### Debug Commands
```bash
# Check webhook endpoint
curl -X POST http://localhost:3001/api/webhooks/eloh/remittance \
  -H "Content-Type: application/json" \
  -d '{"test": "payload"}'

# Check API routes
curl http://localhost:3001/api/quikr/remittances/history
curl "http://localhost:3001/api/quikr/remittances/details?id=YOUR_ID"
```

## Test Data Templates

### Webhook Payload Example
```json
{
  "event_type": "remittance.status_updated",
  "data": {
    "eloh_remittance_id": "eloh_rem_123456789",
    "quikrsolutions_remittance_id": "YOUR_REMITTANCE_ID",
    "status": "processing",
    "previous_status": "pending",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Form Test Data
```javascript
const testRemittance = {
  sendAmount: 100,
  sendCurrency: "USD",
  receiveCurrency: "XCD",
  recipientFirstName: "John",
  recipientLastName: "Doe",
  recipientCountry: "Dominica",
  recipientPhoneNumber: "+1767123456",
  recipientEmail: "<EMAIL>",
  purposeOfRemittance: "Family Support"
};
```

## Success Criteria
- [ ] Form submission works without errors
- [ ] Database records created and updated correctly
- [ ] Real-time updates function properly
- [ ] All status transitions work
- [ ] Error states handled gracefully
- [ ] UI remains responsive throughout flow
- [ ] No console errors during testing
