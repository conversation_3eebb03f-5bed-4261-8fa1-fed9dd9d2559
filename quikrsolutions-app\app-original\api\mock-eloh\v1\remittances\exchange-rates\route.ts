// src/app/api/mock-eloh/v1/remittances/exchange-rates/route.ts

import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { GetElohExchangeRateParams, ElohExchangeRateSuccessResponse, ElohErrorResponse } from '@/lib/services/eloh/elohTypes';

// Existing POST handler (keeping it in case it's used elsewhere)
export async function POST(req: Request): Promise<NextResponse<ElohExchangeRateSuccessResponse | ElohErrorResponse>> {
  try {
    const params: GetElohExchangeRateParams = await req.json();
    const { fromCurrency, toCurrency, amount } = params;

    if (!fromCurrency || !toCurrency || amount === undefined) {
      return NextResponse.json(
        { status: 'error', message: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Simulate fetching exchange rate
    const mockRate = 2.7; // Example rate: 1 USD = 2.7 XCD
    const mockFee = amount * 0.01; // 1% fee
    const destinationAmount = amount * mockRate;
    const totalCharged = amount + mockFee; // Assuming amount is source amount

    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const elohQuoteId = `eloh_quote_${uuidv4()}`;
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000).toISOString(); // Quote valid for 5 minutes

    const successResponse: ElohExchangeRateSuccessResponse = {
      status: 'success',
      data: {
        rate: mockRate,
        sourceAmount: amount,
        destinationAmount: destinationAmount,
        fee: mockFee,
        totalCharged: totalCharged,
        fromCurrency: fromCurrency,
        toCurrency: toCurrency,
        expiresAt: expiresAt,
        elohQuoteId: elohQuoteId,
      },
    };

    // Manually construct JSON response string
    const responseBodyString = JSON.stringify(successResponse);

    return new NextResponse(responseBodyString, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });

  } catch (error: any) {
    console.error('Error in mock exchange rate endpoint (GET):', error);
    const errorResponse: ElohErrorResponse = {
      status: 'error',
      message: 'Internal mock server error (GET)',
      details: {
        error: error.message,
      },
    };
    const errorBodyString = JSON.stringify(errorResponse);
    return new NextResponse(errorBodyString, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}


// New GET handler for fetching exchange rates via query parameters
export async function GET(req: Request): Promise<NextResponse<ElohExchangeRateSuccessResponse | ElohErrorResponse>> {
  try {
    const { searchParams } = new URL(req.url);
    const fromCurrency = searchParams.get('fromCurrency');
    const toCurrency = searchParams.get('toCurrency');
    const amountString = searchParams.get('amount');

    if (!fromCurrency || !toCurrency || !amountString) {
      return NextResponse.json(
        { status: 'error', message: 'Missing required query parameters' },
        { status: 400 }
      );
    }

    const amount = parseFloat(amountString);
    if (isNaN(amount) || amount <= 0) {
       return NextResponse.json(
        { status: 'error', message: 'Invalid amount parameter' },
        { status: 400 }
      );
    }


    // Simulate fetching exchange rate (same logic as POST for consistency)
    const mockRate = 2.7; // Example rate: 1 USD = 2.7 XCD
    const mockFee = amount * 0.01; // 1% fee
    const destinationAmount = amount * mockRate;
    const totalCharged = amount + mockFee; // Assuming amount is source amount

    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const elohQuoteId = `eloh_quote_${uuidv4()}`;
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000).toISOString(); // Quote valid for 5 minutes

    const successResponse: ElohExchangeRateSuccessResponse = {
      status: 'success',
      data: {
        rate: mockRate,
        sourceAmount: amount,
        destinationAmount: destinationAmount,
        fee: mockFee,
        totalCharged: totalCharged,
        fromCurrency: fromCurrency,
        toCurrency: toCurrency,
        expiresAt: expiresAt,
        elohQuoteId: elohQuoteId,
      },
    };

    // Explicitly construct the response body to ensure the data property is included
    const responseBody: ElohExchangeRateSuccessResponse = {
      status: 'success',
      data: {
        rate: mockRate,
        sourceAmount: amount,
        destinationAmount: destinationAmount,
        fee: mockFee,
        totalCharged: totalCharged,
        fromCurrency: fromCurrency,
        toCurrency: toCurrency,
        expiresAt: expiresAt,
        elohQuoteId: elohQuoteId,
      },
    };

    return NextResponse.json(responseBody, { status: 200 });

  } catch (error: any) {
    console.error('Error in mock exchange rate endpoint (GET):', error);
    const errorResponse: ElohErrorResponse = {
      status: 'error',
      message: 'Internal mock server error (GET)',
      details: {
        error: error.message,
      },
    };
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
