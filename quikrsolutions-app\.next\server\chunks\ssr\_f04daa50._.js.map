{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/app/layout.tsx"], "sourcesContent": ["import './globals.css'\n\nexport const metadata = {\n  title: 'Quikrsolutions.app',\n  description: 'Your entire business, intelligently connected.',\n}\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html lang=\"en\">\n      <body>\n        <main className=\"container mx-auto py-10\">{children}</main>\n      </body>\n    </html>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;sBACC,cAAA,8OAAC;gBAAK,WAAU;0BAA2B;;;;;;;;;;;;;;;;AAInD", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}