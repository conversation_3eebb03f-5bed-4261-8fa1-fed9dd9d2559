self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"40b4048e697ef47b7e7f6a29f138e441b235f42dbd\": {\n      \"workers\": {\n        \"app/(app)/subscriptions/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/subscriptions/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/stripeActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(app)/subscriptions/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"Q/miLRP/eFb12tAkKsBxm0kUy5YKDRqikWQ9itnXmXo=\"\n}"