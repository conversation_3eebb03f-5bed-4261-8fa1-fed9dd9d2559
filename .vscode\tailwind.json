{"version": 1.1, "atDirectives": [{"name": "@import", "description": "Import CSS files or modules", "references": [{"name": "MDN Reference", "url": "https://developer.mozilla.org/en-US/docs/Web/CSS/@import"}]}, {"name": "@apply", "description": "Apply utility classes inline in CSS", "references": [{"name": "Tailwind CSS Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#apply"}]}, {"name": "@layer", "description": "Define CSS layers for organizing styles", "references": [{"name": "Tailwind CSS Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#layer"}]}, {"name": "@theme", "description": "Define theme configuration inline (Tailwind v4)", "references": [{"name": "Tailwind CSS v4 Documentation", "url": "https://tailwindcss.com/docs/v4-beta"}]}, {"name": "@custom-variant", "description": "Define custom variants (Tailwind v4)", "references": [{"name": "Tailwind CSS v4 Documentation", "url": "https://tailwindcss.com/docs/v4-beta"}]}, {"name": "@config", "description": "Configure Tailwind settings inline (Tailwind v4)", "references": [{"name": "Tailwind CSS v4 Documentation", "url": "https://tailwindcss.com/docs/v4-beta"}]}, {"name": "@plugin", "description": "Define plugins inline (Tailwind v4)", "references": [{"name": "Tailwind CSS v4 Documentation", "url": "https://tailwindcss.com/docs/v4-beta"}]}, {"name": "@utility", "description": "Define custom utilities (Tailwind v4)", "references": [{"name": "Tailwind CSS v4 Documentation", "url": "https://tailwindcss.com/docs/v4-beta"}]}, {"name": "@variant", "description": "Define variants (Tailwind v4)", "references": [{"name": "Tailwind CSS v4 Documentation", "url": "https://tailwindcss.com/docs/v4-beta"}]}]}