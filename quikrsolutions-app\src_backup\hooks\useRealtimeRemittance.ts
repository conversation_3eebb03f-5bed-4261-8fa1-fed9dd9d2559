'use client';

'use client';

import { useEffect, useState } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client'; // Import client-side Supabase client
import { Database } from '@/types/supabase'; // Import Database types
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js'; // Import Realtime payload type

// Define the type for the data received from Realtime updates
type RemittanceTransactionRow = Database['public']['Tables']['remittance_transactions']['Row'];

// This hook will subscribe to Realtime updates for remittance transactions
export function useRealtimeRemittance(remittanceId?: string) {
  const [remittanceDetails, setRemittanceDetails] = useState<RemittanceTransactionRow | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const supabase = createSupabaseBrowserClient(); // Get client-side Supabase client

    // Set initial loading state
    setLoading(true);
    setError(null);

    // Define the channel name - can be static or dynamic
    const channelName = remittanceId ? `remittance_${remittanceId}` : 'remittance_updates';

    // Set up the Realtime subscription
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE', // Listen specifically for UPDATE events
          schema: 'public', // Your schema name
          table: 'remittance_transactions', // Your table name
          filter: remittanceId ? `quikrsolutions_remittance_id=eq.${remittanceId}` : undefined, // Filter by ID if provided
        },
        (payload: RealtimePostgresChangesPayload<RemittanceTransactionRow>) => { // Add explicit type for payload
          console.log('Realtime update received:', payload);
          // Update the state with the new data, ensuring payload.new is not an empty object
          if (payload.new && Object.keys(payload.new).length > 0) {
            setRemittanceDetails(payload.new as RemittanceTransactionRow); // Explicitly cast to the correct type
          }
        }
      )
      .subscribe((status: string) => { // Add explicit type for status
        console.log('Realtime subscription status:', status);
        if (status === 'SUBSCRIBED') {
          setLoading(false); // Subscription is active
        } else if (status === 'CHANNEL_ERROR') {
          setError('Realtime subscription error.');
          setLoading(false);
        } else if (status === 'TIMED_OUT') {
           setError('Realtime subscription timed out.');
           setLoading(false);
        }
      });

    // Cleanup function to unsubscribe when the component unmounts or remittanceId changes
    return () => {
      console.log('Unsubscribing from Realtime channel:', channelName);
      supabase.removeChannel(channel);
    };

  }, [remittanceId]); // Re-run effect if remittanceId changes

  // Return the remittance details, loading state, and error state
  return { remittanceDetails, loading, error };
}
