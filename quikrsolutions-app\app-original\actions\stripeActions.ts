// src/app/actions/stripeActions.ts
'use server';

import { stripe } from '@/lib/stripe/server'; // Your server-side Stripe client
import { createSupabaseServerClient } from '@/lib/supabase/server'; // For getting user session
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import <PERSON><PERSON> from 'stripe';

// Helper function to get base URL for redirect URLs
async function getURL(path: string = '') { // Make the function async
  const headersInstance = await headers(); // Await the headers() result
  const host = headersInstance.get('host'); // Access get() on the awaited instance
  const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
  return `${protocol}://${host}${path}`;
}

interface CreateCheckoutSessionArgs {
  priceId: string;
  // You might add quantity, metadata, etc. later
}

export async function createCheckoutSession(args: CreateCheckoutSessionArgs) {
  const { priceId } = args;
  const supabase = await createSupabaseServerClient(); // Use await here

  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    console.error('User not authenticated for creating checkout session:', userError);
    // Could return an error object or redirect to login
    // For simplicity, let's assume this action is called from an authenticated context
    // and a proper redirect would happen at the page level if no user.
    // However, redirecting here is also an option:
    // return redirect('/login?message=Authentication required');
    return { error: 'User not authenticated.' };
  }

  // Optional: Check if user already has an active subscription to this or another plan
  // This logic would involve querying your `subscriptions` table in Supabase.
  // For Phase 0, we might skip this complex check and let Stripe handle
  // if a customer tries to subscribe to the same plan twice (it usually prevents it).

  // Optional: Get or create Stripe Customer ID and store it on the user's profile
  // This is best practice for linking Supabase users to Stripe customers.
  let stripeCustomerId: string | undefined;

  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('stripe_customer_id')
    .eq('id', user.id)
    .single();

  if (profileError && profileError.code !== 'PGRST116') { // PGRST116: row not found
    console.error('Error fetching profile for Stripe customer ID:', profileError);
    return { error: 'Could not retrieve user profile.' };
  }

  stripeCustomerId = profile?.stripe_customer_id ?? undefined;

  if (!stripeCustomerId) {
    try {
      const customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          supabaseUserId: user.id,
      },
    } as Stripe.Checkout.SessionCreateParams);
      stripeCustomerId = customer.id;
      // Save the new Stripe Customer ID to the user's profile in Supabase
      const { error: updateProfileError } = await supabase
        .from('profiles')
        .update({ stripe_customer_id: stripeCustomerId })
        .eq('id', user.id);

      if (updateProfileError) {
        console.error('Error updating profile with Stripe Customer ID:', updateProfileError);
        // Non-critical for checkout creation itself, but log it.
      }
    } catch (e: any) {
        console.error('Error creating Stripe customer:', e);
        return { error: `Could not create Stripe customer: ${e.message}` };
    }
  }

  try {
    const successUrl = await getURL('/dashboard?checkout_session_id={CHECKOUT_SESSION_ID}&status=success');
    const cancelUrl = await getURL('/subscriptions?status=cancelled');

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription', // For one-time payments, use 'payment'
      customer: stripeCustomerId, // Use existing or newly created Stripe Customer ID
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      // Define success and cancel URLs
      // These are URLs Stripe will redirect the user to after payment.
      success_url: await getURL('/dashboard?checkout_session_id={CHECKOUT_SESSION_ID}&status=success'), // Or a dedicated success page
      cancel_url: await getURL('/subscriptions?status=cancelled'),
      // Optional: Pass metadata to Stripe session, useful for webhooks
      metadata: {
        supabaseUserId: user.id,
        priceId: priceId,
        // any other info you want to track
      },
    });

    if (session.url) {
      // If using this server action from a client component form,
      // you might want to return the session.url or session.id
      // and let the client handle the redirect.
      // For a direct form submission to a server action, redirecting here is fine.
      // return redirect(session.url); // This won't work directly if action is called via fetch from client
      return { sessionId: session.id, url: session.url, error: null };
    } else {
      return { error: 'Failed to create Stripe session.' };
    }

  } catch (e: any) {
    console.error('Error creating Stripe checkout session:', e);
    return { error: `Could not create checkout session: ${e.message}` };
  }
}

export async function cancelSubscriptionAction(stripeSubscriptionId: string): Promise<{ error?: string; success?: string }> {
  'use server'; // Ensure this is treated as a server action

  const supabase = await createSupabaseServerClient();
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    console.error('User not authenticated for cancelling subscription:', userError);
    return { error: 'User not authenticated. Please log in again.' };
  }

  if (!stripeSubscriptionId) {
    console.error('Stripe Subscription ID is required to cancel.');
    return { error: 'Subscription ID is missing.' };
  }

  try {
    // Optional: Verify that the subscription belongs to the user
    // This would involve fetching the subscription from your DB and checking user_id
    // For now, we assume the ID passed is correct and belongs to the user if they see the button.

    const updatedSubscription = await stripe.subscriptions.update(
      stripeSubscriptionId,
      { cancel_at_period_end: true }
    );

    if (updatedSubscription.cancel_at_period_end) {
      // Optionally, you might want to immediately update your local database here
      // to reflect that cancellation is pending, though the webhook will also handle this.
      // For example:
      // await supabase
      //   .from('subscriptions')
      //   .update({ status: 'pending_cancellation', cancel_at_period_end: true, /* or a specific cancel_at timestamp */ })
      //   .eq('stripe_subscription_id', stripeSubscriptionId)
      //   .eq('user_id', user.id);

      return { success: 'Subscription cancellation requested. It will be cancelled at the end of the current billing period.' };
    } else {
      console.error('Stripe did not confirm cancel_at_period_end for subscription:', stripeSubscriptionId, updatedSubscription);
      return { error: 'Failed to request subscription cancellation from Stripe. Please try again.' };
    }

  } catch (e: any) {
    console.error('Error cancelling Stripe subscription:', e);
    // More specific error handling based on Stripe error codes could be added here
    if (e.code === 'resource_missing') {
        return { error: `Could not find subscription with ID: ${stripeSubscriptionId}. It might have been already cancelled or deleted.` };
    }
    return { error: `Could not cancel subscription: ${e.message}` };
  }
}

export async function createStripePortalSession(): Promise<{ error?: string; url?: string }> {
  'use server';

  const supabase = await createSupabaseServerClient();
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    console.error('User not authenticated for creating Stripe portal session:', userError);
    return { error: 'User not authenticated. Please log in again.' };
  }

  let stripeCustomerId: string | undefined;

  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('stripe_customer_id')
    .eq('id', user.id)
    .single();

  if (profileError && profileError.code !== 'PGRST116') { // PGRST116: row not found
    console.error('Error fetching profile for Stripe customer ID:', profileError);
    return { error: 'Could not retrieve user profile.' };
  }

  stripeCustomerId = profile?.stripe_customer_id ?? undefined;

  if (!stripeCustomerId) {
    console.error('Stripe Customer ID not found for user:', user.id);
    // This could happen if the webhook didn't run or failed to update the profile
    // Or if the user somehow skipped the checkout process that creates a customer.
    // For a portal, a customer ID is essential.
    return { error: 'Stripe customer information not found. Please complete a subscription first or contact support.' };
  }

  try {
    const returnUrl = await getURL('/dashboard'); // Return to the dashboard

    const portalSession = await stripe.billingPortal.sessions.create({
      customer: stripeCustomerId,
      return_url: returnUrl,
    });

    if (portalSession.url) {
      return { url: portalSession.url };
    } else {
      console.error('Failed to create Stripe portal session: No URL returned');
      return { error: 'Failed to create customer portal session.' };
    }
  } catch (e: any) {
    console.error('Error creating Stripe portal session:', e);
    return { error: `Could not create customer portal session: ${e.message}` };
  }
}
