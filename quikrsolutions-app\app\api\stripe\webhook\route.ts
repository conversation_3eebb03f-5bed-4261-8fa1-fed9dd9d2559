// src/app/api/stripe/webhook/route.ts
import { Stripe } from 'stripe';
import { stripe } from '@/lib/stripe/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { NextRequest, NextResponse } from 'next/server';

export const config = {
  api: {
    bodyParser: false, // Required for Stripe webhook signature verification
  },
};

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(req: NextRequest) {
console.log('!!! STRIPE WEBHOOK ENDPOINT HIT !!!');
  if (!webhookSecret) {
    console.error('🔴 STRIPE_WEBHOOK_SECRET is not set.');
    return NextResponse.json({ error: 'Webhook secret not configured.' }, { status: 500 });
  }

  const sig = req.headers.get('stripe-signature');
  let rawBody;
  try {
    rawBody = await req.text();
  } catch (e: any) {
    console.error('🔴 Failed to read request body for webhook:', e.message);
    return NextResponse.json({ error: 'Failed to read request body' }, { status: 400 });
  }

  if (!sig || !rawBody) {
    console.error('🔴 Webhook signature or body missing.');
    return NextResponse.json({ error: 'Missing signature or body.' }, { status: 400 });
  }

  let event: Stripe.Event;
  try {
    event = stripe.webhooks.constructEvent(rawBody, sig, webhookSecret);
  } catch (err: any) {
    console.error(`❌ Stripe Webhook signature verification failed: ${err.message}`);
    return NextResponse.json({ error: `Webhook Error: ${err.message}` }, { status: 400 });
  }

  console.log(`✅ Received Stripe event: ${event.type}, ID: ${event.id}`);

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session;
        console.log(`⏳ Processing checkout.session.completed for session ID: ${session.id}`);

        if (session.payment_status === 'paid') {
          const supabaseUserId = session.metadata?.supabaseUserId;
          const priceId = session.metadata?.priceId; // This is your plan_id
          const stripeSubscriptionId = session.subscription as string;
          const stripeCustomerId = session.customer as string;

          if (!supabaseUserId || !priceId || !stripeSubscriptionId || !stripeCustomerId) {
            console.error(`🔴 Missing metadata or IDs in checkout.session.completed: supabaseUserId: ${supabaseUserId}, priceId: ${priceId}, subscription: ${stripeSubscriptionId}, customer: ${stripeCustomerId}`);
            break; 
          }

          console.log(`Checkout session ${session.id} details: supabaseUserId: ${supabaseUserId}, priceId: ${priceId}, stripeSubscriptionId: ${stripeSubscriptionId}`);

          // Retrieve the full subscription object for accurate status and period dates
          const subscriptionDetails: Stripe.Subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId);
          console.log(`Retrieved Stripe subscription ${subscriptionDetails.id} details.`);
console.log(`DEBUG: Subscription details for ID ${subscriptionDetails.id}:
  Status: ${subscriptionDetails.status},
  Current Period Start Raw: ${(subscriptionDetails as any).current_period_start}, Type: ${typeof (subscriptionDetails as any).current_period_start},
  Current Period End Raw: ${(subscriptionDetails as any).current_period_end}, Type: ${typeof (subscriptionDetails as any).current_period_end}
`);

          const { error: subUpsertError } = await supabaseAdmin
            .from('subscriptions')
            .upsert(
              {
                user_id: supabaseUserId,
                stripe_subscription_id: stripeSubscriptionId,
                plan_id: priceId,
                status: subscriptionDetails.status,
                current_period_start: typeof (subscriptionDetails as any).current_period_start === 'number' ? new Date((subscriptionDetails as any).current_period_start * 1000).toISOString() : null,
                current_period_end: typeof (subscriptionDetails as any).current_period_end === 'number' ? new Date((subscriptionDetails as any).current_period_end * 1000).toISOString() : null,
                cancel_at_period_end: subscriptionDetails.cancel_at_period_end,
              },
              {
                onConflict: 'stripe_subscription_id',
                ignoreDuplicates: false,
              }
            );

          if (subUpsertError) {
            console.error(`🔴 Error upserting subscription ${stripeSubscriptionId} for user ${supabaseUserId}:`, subUpsertError.message);
          } else {
            console.log(`✅ Subscription ${stripeSubscriptionId} upserted successfully for user ${supabaseUserId}.`);
          }

          // Handle payment record
          if (session.payment_intent) {
            const paymentIntentId = session.payment_intent as string;
            const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
            console.log(`Retrieved payment intent ${paymentIntent.id} for checkout session.`);
            
            const { data: subData, error: subSelectError } = await supabaseAdmin
              .from('subscriptions')
              .select('id')
              .eq('stripe_subscription_id', stripeSubscriptionId)
              .single();

            if (subSelectError || !subData) {
                console.error(`🔴 Error fetching subscription ID from DB for stripe_subscription_id ${stripeSubscriptionId}:`, subSelectError?.message || "Subscription not found");
            } else {
                const { error: paymentInsertError } = await supabaseAdmin
                .from('payments')
                .insert({
                  user_id: supabaseUserId,
                  subscription_id: subData.id, // Internal UUID from our subscriptions table
                  amount: paymentIntent.amount / 100, // Convert cents to decimal
                  currency: paymentIntent.currency,
                  payment_gateway: 'stripe',
                  gateway_payment_id: paymentIntent.id,
                  status: paymentIntent.status,
                });

              if (paymentInsertError) {
                console.error(`🔴 Error inserting payment for PI ${paymentIntent.id} (user ${supabaseUserId}):`, paymentInsertError.message);
              } else {
                console.log(`✅ Payment for PI ${paymentIntent.id} (user ${supabaseUserId}) inserted successfully.`);
              }
            }
          } else {
            console.log(`No payment_intent found for checkout session ${session.id}. Skipping payment record.`);
          }
        } else {
          console.warn(`Checkout session ${session.id} completed but payment_status is '${session.payment_status}'. No action taken.`);
        }
        break;

      case 'customer.subscription.updated':
        const updatedSubscription = event.data.object as Stripe.Subscription;
        console.log(`⏳ Processing customer.subscription.updated for subscription ID: ${updatedSubscription.id}, Status: ${updatedSubscription.status}`);

        const { error: subUpdateError } = await supabaseAdmin
          .from('subscriptions')
          .update({
            status: updatedSubscription.status,
            current_period_start: typeof (updatedSubscription as any).current_period_start === 'number' ? new Date((updatedSubscription as any).current_period_start * 1000).toISOString() : null,
            current_period_end: typeof (updatedSubscription as any).current_period_end === 'number' ? new Date((updatedSubscription as any).current_period_end * 1000).toISOString() : null,
            cancel_at_period_end: updatedSubscription.cancel_at_period_end,
          })
          .eq('stripe_subscription_id', updatedSubscription.id);

        if (subUpdateError) {
          console.error(`🔴 Error updating subscription ${updatedSubscription.id} in DB:`, subUpdateError.message);
        } else {
          console.log(`✅ Subscription ${updatedSubscription.id} updated successfully in DB.`);
        }
        break;

      case 'customer.subscription.deleted':
        const deletedSubscription = event.data.object as Stripe.Subscription;
        console.log(`⏳ Processing customer.subscription.deleted for subscription ID: ${deletedSubscription.id}, Status: ${deletedSubscription.status}`);
        
        const { error: subDeleteError } = await supabaseAdmin
          .from('subscriptions')
          .update({
            status: deletedSubscription.status, // e.g., 'canceled'
            current_period_end: typeof (deletedSubscription as any).current_period_end === 'number' ? new Date((deletedSubscription as any).current_period_end * 1000).toISOString() : null,
            // Optionally clear cancel_at_period_end or set other fields as appropriate
          })
          .eq('stripe_subscription_id', deletedSubscription.id);

        if (subDeleteError) {
          console.error(`🔴 Error marking subscription ${deletedSubscription.id} as ${deletedSubscription.status} in DB:`, subDeleteError.message);
        } else {
          console.log(`✅ Subscription ${deletedSubscription.id} marked as ${deletedSubscription.status} successfully in DB.`);
        }
        break;

      case 'invoice.payment_succeeded':
        const invoice = event.data.object as Stripe.Invoice;
        console.log(`⏳ Processing invoice.payment_succeeded for invoice ID: ${invoice.id}`);

        // @ts-ignore
        if (invoice.billing_reason === 'subscription_cycle' && invoice.subscription && invoice.customer && invoice.payment_intent) {
          const stripeCustomerId = invoice.customer as string;
          // @ts-ignore
          const stripeSubscriptionId = invoice.subscription as string;
          // @ts-ignore
          const paymentIntentId = invoice.payment_intent as string;

          // @ts-ignore
          console.log(`Invoice ${invoice.id} is for subscription_cycle. Customer: ${stripeCustomerId}, Subscription: ${stripeSubscriptionId}, PI: ${paymentIntentId}`);
          
          const customerObject = await stripe.customers.retrieve(stripeCustomerId);
          let supabaseUserId: string | undefined;

          if (customerObject && !customerObject.deleted) {
            supabaseUserId = (customerObject as Stripe.Customer).metadata?.supabaseUserId;
          }

          if (!supabaseUserId) {
            console.warn(`🔴 supabaseUserId not found on customer ${stripeCustomerId} for invoice ${invoice.id}. Skipping payment record.`);
            break;
          }
          console.log(`Retrieved supabaseUserId ${supabaseUserId} from customer ${stripeCustomerId}.`);

          const { data: subDataInvoice, error: subSelectInvoiceError } = await supabaseAdmin
            .from('subscriptions')
            .select('id')
            .eq('stripe_subscription_id', stripeSubscriptionId)
            .single();
          
          if (subSelectInvoiceError || !subDataInvoice) {
            console.error(`🔴 Error fetching subscription ID from DB for stripe_subscription_id ${stripeSubscriptionId} (invoice ${invoice.id}):`, subSelectInvoiceError?.message || "Subscription not found");
          } else {
            const { error: invoicePaymentError } = await supabaseAdmin
              .from('payments')
              .insert({
                user_id: supabaseUserId,
                subscription_id: subDataInvoice.id,
                amount: invoice.amount_paid / 100, // amount_paid is in cents
                currency: invoice.currency,
                payment_gateway: 'stripe',
                gateway_payment_id: paymentIntentId, 
                status: invoice.status ?? 'succeeded', // invoice.status can be 'paid', 'open', 'void', etc. 'succeeded' is a safe default for payment_succeeded
              });

            if (invoicePaymentError) {
              console.error(`🔴 Error inserting payment for invoice ${invoice.id} (PI ${paymentIntentId}, user ${supabaseUserId}):`, invoicePaymentError.message);
            } else {
              console.log(`✅ Payment for invoice ${invoice.id} (PI ${paymentIntentId}, user ${supabaseUserId}) inserted successfully.`);
            }
          }
        } else {
          // @ts-ignore
          console.log(`Invoice ${invoice.id} not processed: billing_reason: ${invoice.billing_reason}, subscription: ${invoice.subscription}, customer: ${invoice.customer}, payment_intent: ${invoice.payment_intent}`);
        }
        break;

      default:
        console.warn(`🤷‍♀️ Unhandled event type: ${event.type}`);
    }
  } catch (error: any) {
    console.error(`🔴 Error processing webhook event ${event.type} (ID: ${event.id}):`, error.message, error.stack);
    // Return 200 to Stripe to acknowledge receipt and prevent retries for internal processing errors.
    // Stripe considers any non-2xx response as a failure and will retry.
    return NextResponse.json({ error: 'Webhook processing error occurred.', received: true }, { status: 200 });
  }

  return NextResponse.json({ received: true });
}
