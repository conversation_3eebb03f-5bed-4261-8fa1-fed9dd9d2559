{"mcpServers": {"Context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "alwaysAllow": ["search", "resolve-library-id", "get-library-docs"], "disabled": false}, "browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"], "alwaysAllow": []}, "@21st-dev/magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest", "API_KEY=\"your-api-key\""], "alwaysAllow": ["21st_magic_component_builder"]}}}