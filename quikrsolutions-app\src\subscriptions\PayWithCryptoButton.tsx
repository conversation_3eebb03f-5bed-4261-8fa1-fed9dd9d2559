"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";

interface PayWithCryptoButtonProps {
  amount: number;
  currency: string;
  planId: string;
  userId: string;
  onOrderCreated: (orderId: string) => void;
}

const PayWithCryptoButton: React.FC<PayWithCryptoButtonProps> = ({ amount, currency, planId, userId, onOrderCreated }) => {
  const [isCryptoPaymentPending, setIsCryptoPaymentPending] = useState(false);

  const { toast } = useToast();

  const handlePayWithCrypto = async () => {
    setIsCryptoPaymentPending(true);

    try {
      const response = await fetch('/api/mock-eloh/v1/btcpay/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amountFiat: amount,
          fiatCurrency: currency,
          description: `Payment for plan ${planId || 'subscription'}`,
          buyerEmail: '<EMAIL>', // Replace with actual email
          redirectUrl: `${window.location.origin}/payment/eloh/success?userId=${userId}`, // Remove orderId from here
          notificationUrl: `${window.location.origin}/api/webhooks/eloh/btcpay`,
          userId: userId
        }),
      });

      if (!response.ok) {
        let errorMessage = `Payment initiation failed. Status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage += ` - ${errorData.message || 'No details provided'}`;
        } catch (parseError) {
          console.warn("Failed to parse error response:", parseError);
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();

      if (data.status === 'success') {
        console.log('Redirecting to:', data.data.btcpayInvoiceUrl);
        onOrderCreated(data.data.quikrsolutionsOrderId); // Get orderId from response
        window.location.href = data.data.btcpayInvoiceUrl;
      } else {
        console.error('Eloh API error:', data);
        throw new Error(`Eloh API Error: ${data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('Error calling Eloh API:', error);
      toast({
        title: "Payment Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsCryptoPaymentPending(false);
    }
  };

  return (
    <Button onClick={handlePayWithCrypto} disabled={isCryptoPaymentPending}>
      {isCryptoPaymentPending ? "Redirecting to Crypto Payment..." : "Pay with Crypto"}
    </Button>
  );
};

export default PayWithCryptoButton;
