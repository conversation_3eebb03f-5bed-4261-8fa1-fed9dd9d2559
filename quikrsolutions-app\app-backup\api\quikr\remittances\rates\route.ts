// src/app/api/quikr/remittances/rates/route.ts

import { NextResponse } from 'next/server';
import { getExchangeRate } from '@/lib/services/eloh/elohRemittanceService';
import { GetElohExchangeRateParams, ElohErrorResponse } from '@/lib/services/eloh/elohTypes';

export interface QuikrExchangeRateApiResponse {
  status: 'success';
  data: {
    rate: number;
    sourceAmount: number;
    destinationAmount: number;
    fee: number;
    totalCharged: number;
    fromCurrency: string;
    toCurrency: string;
    expiresAt: string;
    elohQuoteId?: string; // If Eloh provides one in its response
  };
}

interface QuikrApiErrorResponse {
  status: 'error';
  message: string;
  details?: Record<string, any>;
}

export type QuikrExchangeRateResponse = QuikrExchangeRateApiResponse | QuikrApiErrorResponse;

export async function GET(request: Request): Promise<NextResponse<QuikrExchangeRateResponse>> {
  try {
    const { searchParams } = new URL(request.url);
    const fromCurrency = searchParams.get('fromCurrency');
    const toCurrency = searchParams.get('toCurrency');
    const amount = searchParams.get('amount');

    if (!fromCurrency || !toCurrency || !amount) {
      return NextResponse.json(
        { status: 'error', message: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const amountNumber = parseFloat(amount);
    if (isNaN(amountNumber)) {
      return NextResponse.json(
        { status: 'error', message: 'Invalid amount' },
        { status: 400 }
      );
    }

    const params: GetElohExchangeRateParams = {
      fromCurrency,
      toCurrency,
      amount: amountNumber,
    };

    const result = await getExchangeRate(params);

    if (result.status === 'success') {
      const { rate, sourceAmount, destinationAmount, fee, totalCharged, fromCurrency, toCurrency, expiresAt } = result.data;
      const responseData: QuikrExchangeRateApiResponse = {
        status: 'success',
        data: {
          rate,
          sourceAmount,
          destinationAmount,
          fee,
          totalCharged,
          fromCurrency,
          toCurrency,
          expiresAt,
          elohQuoteId: result.data.elohQuoteId, // If Eloh provides a quote ID
        },
      };
      return NextResponse.json(responseData, { status: 200 });
    } else {
      const errorResult = result as ElohErrorResponse;
      return NextResponse.json(
        { status: 'error', message: errorResult.message, details: errorResult.details },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error getting exchange rate:', error);
    return NextResponse.json(
      { status: 'error', message: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
