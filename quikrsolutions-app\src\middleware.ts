import { createServerClient } from './lib/supabase/server'; // Corrected import path
import { NextResponse, type NextRequest } from 'next/server';
import { type CookieOptions } from '@supabase/ssr'; // Import CookieOptions

// src/middleware.ts
// ... (imports) ...
export async function middleware(request: NextRequest) {
  console.log('--- Middleware running for path:', request.nextUrl.pathname);

  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get: (name: string) => request.cookies.get(name)?.value,
        set: (name: string, value: string, options: CookieOptions) => { // Changed any to CookieOptions
          request.cookies.set({ name, value, ...options });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({ name, value, ...options });
        },
        remove: (name: string, options: CookieOptions) => { // Changed any to <PERSON>ieOptions
          request.cookies.set({ name, value: '', ...options });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({ name, value: '', ...options });
        },
      },
    }
  );

  const { data: { session } } = await supabase.auth.getSession(); // Crucial: This fetches the session recognized by the server
  console.log('Middleware session status:', session ? `User ID: ${session.user.id}` : 'No session');

  const { pathname } = request.nextUrl;

  const authRoutes = ['/login', '/signup']; // Define auth routes

  const isAuthRoute = authRoutes.includes(pathname);

  if (session && isAuthRoute) {
    console.log(`Middleware: Session exists (User: ${session.user.id}) AND on auth route (${pathname}). Redirecting to /dashboard.`);
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }
  // ... (other conditions) ...
  console.log('Middleware: No redirect condition met. Proceeding.');
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
