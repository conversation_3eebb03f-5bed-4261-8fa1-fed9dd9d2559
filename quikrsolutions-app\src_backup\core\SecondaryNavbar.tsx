import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const SecondaryNavbar = () => {
  return (
    <div className="w-full h-10 bg-gray-100 border-b border-gray-200 flex items-center justify-between px-4">
      {/* Weather Display */}
      <div className="flex items-center">
        <CloudSun className="mr-2 h-4 w-4" />
        <span>25°C, Sunny</span>
      </div>

      {/* Marquee Component */}
      <div className="marquee">
        <span className="marquee-text">
          Platform Update: Enhanced AI tools available in 'More' section! | Welcome to Quikrsolutions - Your All-in-One Hub! | Special: Get 10% off your first FoodCourt order this week!
        </span>
      </div>

      {/* Platform Notifications Icon & Popover */}
      <div>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon">
              <Bell className="h-4 w-4" />
              <span className="sr-only">Notifications</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-4">
            <div className="grid gap-4">
              <p className="text-sm font-medium">Notifications</p>
              <div className="grid gap-2">
                <div className="border-b border-gray-200 pb-2">
                  <p className="text-sm">New feature: Remittances now live!</p>
                </div>
                <div className="border-b border-gray-200 pb-2">
                  <p className="text-sm">Scheduled maintenance on Sunday at 2 AM UTC.</p>
                </div>
                <div>
                  <p className="text-sm">Special: Get 10% off your first FoodCourt order this week!</p>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};

export default SecondaryNavbar;