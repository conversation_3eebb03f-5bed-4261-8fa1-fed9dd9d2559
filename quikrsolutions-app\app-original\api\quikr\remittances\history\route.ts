import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { Database } from '@/types/supabase';

type RemittanceTransactionRow = Database['public']['Tables']['remittance_transactions']['Row'];

export interface RemittanceHistoryResponse {
  status: 'success';
  data: RemittanceTransactionRow[];
}

export interface RemittanceHistoryErrorResponse {
  status: 'error';
  message: string;
  details?: Record<string, any>;
}

export async function GET(request: Request) {
  try {
    console.log('Remittance history API called');
    const supabase = createSupabaseServerClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    console.log('Auth check result:', { user: user?.id, authError: authError?.message });

    if (authError || !user) {
      console.log('No authenticated user, returning empty array');
      // For development, return empty array instead of error
      return NextResponse.json({
        status: 'success',
        data: []
      } as RemittanceHistoryResponse);
    }

    // Fetch remittance transactions for the current user
    console.log('Fetching remittances for user:', user.id);
    const { data: remittances, error: fetchError } = await supabase
      .from('remittance_transactions')
      .select('*')
      .eq('sender_user_id', user.id)
      .order('created_at', { ascending: false });

    console.log('Fetch result:', { remittances: remittances?.length, fetchError: fetchError?.message });

    if (fetchError) {
      console.error('Error fetching remittance history:', fetchError);
      return NextResponse.json(
        {
          status: 'error',
          message: 'Failed to fetch remittance history',
          details: { fetchError: fetchError.message }
        } as RemittanceHistoryErrorResponse,
        { status: 500 }
      );
    }

    console.log('Returning remittances:', remittances?.length || 0);
    return NextResponse.json({
      status: 'success',
      data: remittances || []
    } as RemittanceHistoryResponse);

  } catch (error: any) {
    console.error('Unexpected error in remittance history API:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'An unexpected error occurred',
        details: { error: error.message }
      } as RemittanceHistoryErrorResponse,
      { status: 500 }
    );
  }
}
