/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(auth)/login/page";
exports.ids = ["app/(auth)/login/page"];
exports.modules = {

/***/ "(rsc)/./app/(auth)/layout.tsx":
/*!*******************************!*\
  !*** ./app/(auth)/layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// src/app/(auth)/layout.tsx\n\n\nfunction AuthLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"flex min-h-screen flex-col items-center justify-center bg-gradient-to-br from-slate-900 to-slate-800 p-4\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\layout.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvKGF1dGgpL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQSw0QkFBNEI7O0FBQ0Y7QUFFWCxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsV0FBVTtrQkFFYkY7Ozs7OztBQUdQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNrbGxpXFxEZXNrdG9wXFxxdWlrcnNvbHV0aW9uc19wbGF0Zm9ybVxccXVpa3Jzb2x1dGlvbnMtYXBwXFxhcHBcXChhdXRoKVxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvYXBwLyhhdXRoKS9sYXlvdXQudHN4XHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBdXRoTGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufToge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleCBtaW4taC1zY3JlZW4gZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtOTAwIHRvLXNsYXRlLTgwMCBwLTRcIj5cclxuICAgICAgey8qIFlvdSBjb3VsZCBhZGQgYSBsb2dvIG9yIGFwcCBuYW1lIGhlcmUgdXNpbmcgR3JhZGllbnRUZXh0IGxhdGVyICovfVxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L21haW4+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJSZWFjdCIsIkF1dGhMYXlvdXQiLCJjaGlsZHJlbiIsIm1haW4iLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/(auth)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/(auth)/login/page.tsx":
/*!***********************************!*\
  !*** ./app/(auth)/login/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Desktop\\\\\\\\quikrsolutions_platform\\\\\\\\quikrsolutions-app\\\\\\\\app\\\\\\\\(auth)\\\\\\\\login\\\\\\\\page.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/(auth)/login/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9977bbd481fc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcU2tsbGlcXERlc2t0b3BcXHF1aWtyc29sdXRpb25zX3BsYXRmb3JtXFxxdWlrcnNvbHV0aW9ucy1hcHBcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5OTc3YmJkNDgxZmNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var geist_font_sans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! geist/font/sans */ \"(rsc)/./node_modules/geist/dist/sans.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _contexts_authcontext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/authcontext */ \"(rsc)/./src/contexts/authcontext.tsx\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./src/components/ui/sonner.tsx\");\n/* harmony import */ var _components_core_PrimaryNavbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/core/PrimaryNavbar */ \"(rsc)/./src/components/core/PrimaryNavbar.tsx\");\n/* harmony import */ var _components_core_SecondaryNavbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/core/SecondaryNavbar */ \"(rsc)/./src/components/core/SecondaryNavbar.tsx\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-themes */ \"(rsc)/./node_modules/next-themes/dist/index.mjs\");\n// src/app/layout.tsx\n\n // Or Inter, or your chosen font\n// import { GeistMono } from 'geist/font/mono'; // If using\n\n // Import AuthProvider\n // Import Sonner Toaster\n\n\n\nconst metadata = {\n    title: 'Quikrsolutions.app',\n    description: 'Your entire business, intelligently connected.'\n};\nasync function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${geist_font_sans__WEBPACK_IMPORTED_MODULE_1__.GeistSans.variable}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"icon\",\n                    href: \"/favicon.ico\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_7__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"system\",\n                    enableSystem: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_authcontext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_core_PrimaryNavbar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_core_SecondaryNavbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"container mx-auto py-10\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                                richColors: true,\n                                position: \"top-right\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(auth)/layout.tsx */ \"(rsc)/./app/(auth)/layout.tsx\"));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(auth)/login/page.tsx */ \"(rsc)/./app/(auth)/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(auth)',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\layout.tsx\"],\n'not-found': [module5, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module6, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module7, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(auth)/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(auth)/login/page.tsx */ \"(rsc)/./app/(auth)/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NrbGxpJTVDJTVDRGVza3RvcCU1QyU1Q3F1aWtyc29sdXRpb25zX3BsYXRmb3JtJTVDJTVDcXVpa3Jzb2x1dGlvbnMtYXBwJTVDJTVDYXBwJTVDJTVDKGF1dGgpJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQXlJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxTa2xsaVxcXFxEZXNrdG9wXFxcXHF1aWtyc29sdXRpb25zX3BsYXRmb3JtXFxcXHF1aWtyc29sdXRpb25zLWFwcFxcXFxhcHBcXFxcKGF1dGgpXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Ccore%5C%5CPrimaryNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cpopover.tsx%22%2C%22ids%22%3A%5B%22Popover%22%2C%22PopoverTrigger%22%2C%22PopoverContent%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccontexts%5C%5Cauthcontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Ccore%5C%5CPrimaryNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cpopover.tsx%22%2C%22ids%22%3A%5B%22Popover%22%2C%22PopoverTrigger%22%2C%22PopoverContent%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccontexts%5C%5Cauthcontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-themes/dist/index.mjs */ \"(rsc)/./node_modules/next-themes/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/core/PrimaryNavbar.tsx */ \"(rsc)/./src/components/core/PrimaryNavbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/popover.tsx */ \"(rsc)/./src/components/ui/popover.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/sonner.tsx */ \"(rsc)/./src/components/ui/sonner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/authcontext.tsx */ \"(rsc)/./src/contexts/authcontext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Ccore%5C%5CPrimaryNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cpopover.tsx%22%2C%22ids%22%3A%5B%22Popover%22%2C%22PopoverTrigger%22%2C%22PopoverContent%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccontexts%5C%5Cauthcontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTa2xsaVxcRGVza3RvcFxccXVpa3Jzb2x1dGlvbnNfcGxhdGZvcm1cXHF1aWtyc29sdXRpb25zLWFwcFxcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/components/core/PrimaryNavbar.tsx":
/*!***********************************************!*\
  !*** ./src/components/core/PrimaryNavbar.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\quikrsolutions_platform\\quikrsolutions-app\\src\\components\\core\\PrimaryNavbar.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/core/SecondaryNavbar.tsx":
/*!*************************************************!*\
  !*** ./src/components/core/SecondaryNavbar.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_CloudSun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CloudSun!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/cloud-sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CloudSun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CloudSun!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/popover */ \"(rsc)/./src/components/ui/popover.tsx\");\n\n\n\n\n\nconst SecondaryNavbar = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-10 bg-gray-100 border-b border-gray-200 flex items-center justify-between px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CloudSun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"mr-2 h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"25\\xb0C, Sunny\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"marquee\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"marquee-text\",\n                    children: \"Platform Update: Enhanced AI tools available in 'More' section! | Welcome to Quikrsolutions - Your All-in-One Hub! | Special: Get 10% off your first FoodCourt order this week!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CloudSun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                            className: \"w-80 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-200 pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: \"New feature: Remittances now live!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-200 pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Scheduled maintenance on Sunday at 2 AM UTC.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Special: Get 10% off your first FoodCourt order this week!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\SecondaryNavbar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SecondaryNavbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/core/SecondaryNavbar.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwrYkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUNFO1lBQ0ZDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBR0YsU0FBU0ssT0FBTyxFQUNkQyxTQUFTLEVBQ1RiLE9BQU8sRUFDUE8sSUFBSSxFQUNKTyxVQUFVLEtBQUssRUFDZixHQUFHQyxPQUlGO0lBQ0QsTUFBTUMsT0FBT0YsVUFBVW5CLHNEQUFJQSxHQUFHO0lBRTlCLHFCQUNFLDhEQUFDcUI7UUFDQ0MsYUFBVTtRQUNWSixXQUFXaEIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU07UUFBVTtRQUN2RCxHQUFHRSxLQUFLOzs7Ozs7QUFHZjtBQUVpQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTa2xsaVxcRGVza3RvcFxccXVpa3Jzb2x1dGlvbnNfcGxhdGZvcm1cXHF1aWtyc29sdXRpb25zLWFwcFxcc3JjXFxjb21wb25lbnRzXFx1aVxcYnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgU2xvdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2xvdFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgWyZfc3ZnXTpwb2ludGVyLWV2ZW50cy1ub25lIFsmX3N2Zzpub3QoW2NsYXNzKj0nc2l6ZS0nXSldOnNpemUtNCBzaHJpbmstMCBbJl9zdmddOnNocmluay0wIG91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOmJvcmRlci1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1yaW5nLzUwIGZvY3VzLXZpc2libGU6cmluZy1bM3B4XSBhcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS8yMCBkYXJrOmFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzQwIGFyaWEtaW52YWxpZDpib3JkZXItZGVzdHJ1Y3RpdmVcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgXCJiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIHNoYWRvdy14cyBob3ZlcjpiZy1wcmltYXJ5LzkwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYmctZGVzdHJ1Y3RpdmUgdGV4dC13aGl0ZSBzaGFkb3cteHMgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTAgZm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzIwIGRhcms6Zm9jdXMtdmlzaWJsZTpyaW5nLWRlc3RydWN0aXZlLzQwIGRhcms6YmctZGVzdHJ1Y3RpdmUvNjBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBiZy1iYWNrZ3JvdW5kIHNoYWRvdy14cyBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBkYXJrOmJnLWlucHV0LzMwIGRhcms6Ym9yZGVyLWlucHV0IGRhcms6aG92ZXI6YmctaW5wdXQvNTBcIixcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgIFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgc2hhZG93LXhzIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxuICAgICAgICBnaG9zdDpcbiAgICAgICAgICBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGRhcms6aG92ZXI6YmctYWNjZW50LzUwXCIsXG4gICAgICAgIGxpbms6IFwidGV4dC1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC05IHB4LTQgcHktMiBoYXMtWz5zdmddOnB4LTNcIixcbiAgICAgICAgc206IFwiaC04IHJvdW5kZWQtbWQgZ2FwLTEuNSBweC0zIGhhcy1bPnN2Z106cHgtMi41XCIsXG4gICAgICAgIGxnOiBcImgtMTAgcm91bmRlZC1tZCBweC02IGhhcy1bPnN2Z106cHgtNFwiLFxuICAgICAgICBpY29uOiBcInNpemUtOVwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmZ1bmN0aW9uIEJ1dHRvbih7XG4gIGNsYXNzTmFtZSxcbiAgdmFyaWFudCxcbiAgc2l6ZSxcbiAgYXNDaGlsZCA9IGZhbHNlLFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJidXR0b25cIj4gJlxuICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiAmIHtcbiAgICBhc0NoaWxkPzogYm9vbGVhblxuICB9KSB7XG4gIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcblxuICByZXR1cm4gKFxuICAgIDxDb21wXG4gICAgICBkYXRhLXNsb3Q9XCJidXR0b25cIlxuICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNsb3QiLCJjdmEiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInNlY29uZGFyeSIsImdob3N0IiwibGluayIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImNsYXNzTmFtZSIsImFzQ2hpbGQiLCJwcm9wcyIsIkNvbXAiLCJkYXRhLXNsb3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/popover.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/popover.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Popover: () => (/* binding */ Popover),
/* harmony export */   PopoverContent: () => (/* binding */ PopoverContent),
/* harmony export */   PopoverTrigger: () => (/* binding */ PopoverTrigger)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Popover = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\quikrsolutions_platform\\quikrsolutions-app\\src\\components\\ui\\popover.tsx",
"Popover",
);const PopoverTrigger = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PopoverTrigger() from the server but PopoverTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\quikrsolutions_platform\\quikrsolutions-app\\src\\components\\ui\\popover.tsx",
"PopoverTrigger",
);const PopoverContent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PopoverContent() from the server but PopoverContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\quikrsolutions_platform\\quikrsolutions-app\\src\\components\\ui\\popover.tsx",
"PopoverContent",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/sonner.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/sonner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\quikrsolutions_platform\\quikrsolutions-app\\src\\components\\ui\\sonner.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./src/contexts/authcontext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/authcontext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\nconst AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\contexts\\\\authcontext.tsx\",\n\"AuthProvider\",\n);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\contexts\\\\authcontext.tsx\",\n\"useAuth\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/authcontext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTa2xsaVxcRGVza3RvcFxccXVpa3Jzb2x1dGlvbnNfcGxhdGZvcm1cXHF1aWtyc29sdXRpb25zLWFwcFxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./app/(auth)/login/page.tsx":
/*!***********************************!*\
  !*** ./app/(auth)/login/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n// src/app/(auth)/login/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n// import { GradientText } from '@/components/vendor/gradient-text'; // Uncomment if you create this\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_5__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_5__.string().email({\n        message: 'Invalid email address.'\n    }),\n    password: zod__WEBPACK_IMPORTED_MODULE_5__.string().min(1, {\n        message: 'Password is required.'\n    })\n});\nfunction LoginPage() {\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_11__.createSupabaseBrowserClient)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_12__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(loginSchema)\n    });\n    const onSubmit = async (data)=>{\n        setIsLoading(true);\n        try {\n            const { error } = await supabase.auth.signInWithPassword({\n                email: data.email,\n                password: data.password\n            });\n            if (error) {\n                throw error;\n            }\n            toast({\n                title: 'Login Successful!',\n                description: 'Welcome back!'\n            });\n            router.push('/dashboard'); // Middleware will handle redirect after onAuthStateChange\n        } catch (error) {\n            console.error('Login error:', error);\n            toast({\n                title: 'Login Failed',\n                description: error.message || 'Invalid credentials. Please try again.',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        setIsLoading(true);\n        try {\n            const { error } = await supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: `${window.location.origin}/api/auth/callback`\n                }\n            });\n            if (error) throw error;\n        } catch (error) {\n            console.error('Google Sign-In error:', error);\n            toast({\n                title: 'Google Sign-In Failed',\n                description: error.message || 'Could not sign in with Google. Please try again.',\n                variant: 'destructive'\n            });\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n        className: \"w-full max-w-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                className: \"space-y-1 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                        className: \"text-2xl\",\n                        children: \"Welcome Back!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                        children: \"Enter your credentials to access your account\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                className: \"grid gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        className: \"grid gap-4\",\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                        htmlFor: \"email\",\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                        id: \"email\",\n                                        type: \"email\",\n                                        placeholder: \"<EMAIL>\",\n                                        ...register('email'),\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-500\",\n                                        children: errors.email.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_9__.Label, {\n                                        htmlFor: \"password\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                        id: \"password\",\n                                        type: \"password\",\n                                        placeholder: \"********\",\n                                        ...register('password'),\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-500\",\n                                        children: errors.password.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/forgot-password\" // We'll create this page later\n                                    ,\n                                    className: \"text-sm text-muted-foreground hover:underline\",\n                                    children: \"Forgot password?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                type: \"submit\",\n                                className: \"w-full\",\n                                disabled: isLoading,\n                                children: isLoading ? 'Signing In...' : 'Sign In'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-full border-t\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex justify-center text-xs uppercase\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-card px-2 text-muted-foreground\",\n                                    children: \"Or continue with\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"outline\",\n                        className: \"w-full\",\n                        onClick: handleGoogleSignIn,\n                        disabled: isLoading,\n                        children: \"Google\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardFooter, {\n                className: \"flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: [\n                        \"Don't have an account?\",\n                        ' ',\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/signup\",\n                            className: \"font-semibold text-primary hover:underline\",\n                            children: \"Sign Up\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/(auth)/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(auth)/login/page.tsx */ \"(ssr)/./app/(auth)/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NrbGxpJTVDJTVDRGVza3RvcCU1QyU1Q3F1aWtyc29sdXRpb25zX3BsYXRmb3JtJTVDJTVDcXVpa3Jzb2x1dGlvbnMtYXBwJTVDJTVDYXBwJTVDJTVDKGF1dGgpJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQXlJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxTa2xsaVxcXFxEZXNrdG9wXFxcXHF1aWtyc29sdXRpb25zX3BsYXRmb3JtXFxcXHF1aWtyc29sdXRpb25zLWFwcFxcXFxhcHBcXFxcKGF1dGgpXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Ccore%5C%5CPrimaryNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cpopover.tsx%22%2C%22ids%22%3A%5B%22Popover%22%2C%22PopoverTrigger%22%2C%22PopoverContent%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccontexts%5C%5Cauthcontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Ccore%5C%5CPrimaryNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cpopover.tsx%22%2C%22ids%22%3A%5B%22Popover%22%2C%22PopoverTrigger%22%2C%22PopoverContent%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccontexts%5C%5Cauthcontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-themes/dist/index.mjs */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/core/PrimaryNavbar.tsx */ \"(ssr)/./src/components/core/PrimaryNavbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/popover.tsx */ \"(ssr)/./src/components/ui/popover.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/sonner.tsx */ \"(ssr)/./src/components/ui/sonner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/authcontext.tsx */ \"(ssr)/./src/contexts/authcontext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Ccore%5C%5CPrimaryNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cpopover.tsx%22%2C%22ids%22%3A%5B%22Popover%22%2C%22PopoverTrigger%22%2C%22PopoverContent%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSklli%5C%5CDesktop%5C%5Cquikrsolutions_platform%5C%5Cquikrsolutions-app%5C%5Csrc%5C%5Ccontexts%5C%5Cauthcontext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/core/PrimaryNavbar.tsx":
/*!***********************************************!*\
  !*** ./src/components/core/PrimaryNavbar.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/navigation-menu */ \"(ssr)/./src/components/ui/navigation-menu.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nconst PrimaryNavbar = ()=>{\n    const { setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_8__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-900 shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-3 flex items-center justify-between md:flex-row flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 md:flex-row flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/app/feed\",\n                            className: \"font-bold text-lg\",\n                            children: \"Quikrsolutions.app\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/app/feed\",\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenu, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenuList, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenuItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenuTrigger, {\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenuContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/places\",\n                                                            children: \"Places Directory\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 44,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 44,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/marketplace\",\n                                                            children: \"Marketplace Hub\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 45,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 45,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/foodcourt\",\n                                                            children: \"FoodCourt\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 46,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 46,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/products\",\n                                                            children: \"Product Catalogue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 47,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 47,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/services\",\n                                                            children: \"Service Providers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 48,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 48,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/rentals\",\n                                                            children: \"Rentals Hub\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 49,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/app/pricing\",\n                            children: \"Pricing\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenu, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenuList, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenuItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenuTrigger, {\n                                            children: \"More\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation_menu__WEBPACK_IMPORTED_MODULE_3__.NavigationMenuContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/jobs\",\n                                                            children: \"Job Listings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 62,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 62,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/graphic-trends\",\n                                                            children: \"Graphic Trends\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/trends-advice\",\n                                                            children: \"Business Trends & AI Advice\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 64,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/ai-arbitrage\",\n                                                            children: \"AI Arbitrage Engine\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 65,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/ai-tools\",\n                                                            children: \"AI Agentic Tools\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/portfolio\",\n                                                            children: \"Portfolio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/app/contact\",\n                                                            children: \"Contact Us\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/app/about\",\n                            children: \"About\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 md:flex-row flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            type: \"text\",\n                            placeholder: \"Search...\",\n                            className: \"max-w-xs\",\n                            onSubmit: ()=>console.log('Search submitted')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"outline\",\n                            size: \"icon\",\n                            onClick: ()=>setTheme((theme)=>theme === 'dark' ? 'light' : 'dark'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Toggle theme\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"h-8 w-8 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                            className: \"h-8 w-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                                                    src: \"https://github.com/shadcn.png\",\n                                                    alt: \"@shadcn\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                    children: \"CN\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuContent, {\n                                    className: \"w-56\",\n                                    align: \"end\",\n                                    forceMount: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                            children: \"My Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                            children: \"Invoices/Receipts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\core\\\\PrimaryNavbar.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PrimaryNavbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/core/PrimaryNavbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/avatar.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/avatar.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarImage,AvatarFallback auto */ \n\n\n\nfunction Avatar({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"avatar\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex size-8 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction AvatarImage({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        \"data-slot\": \"avatar-image\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square size-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction AvatarFallback({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        \"data-slot\": \"avatar-fallback\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-muted flex size-full items-center justify-center rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/dropdown-menu.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/dropdown-menu.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuPortal,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuGroup,DropdownMenuLabel,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioGroup,DropdownMenuRadioItem,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuSub,DropdownMenuSubTrigger,DropdownMenuSubContent auto */ \n\n\n\n\nfunction DropdownMenu({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"dropdown-menu\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\nfunction DropdownMenuPortal({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        \"data-slot\": \"dropdown-menu-portal\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuTrigger({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        \"data-slot\": \"dropdown-menu-trigger\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuContent({ className, sideOffset = 4, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-slot\": \"dropdown-menu-content\",\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuGroup({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group, {\n        \"data-slot\": \"dropdown-menu-group\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuItem({ className, inset, variant = \"default\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        \"data-slot\": \"dropdown-menu-item\",\n        \"data-inset\": inset,\n        \"data-variant\": variant,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuCheckboxItem({ className, children, checked, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        \"data-slot\": \"dropdown-menu-checkbox-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"size-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuRadioGroup({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n        \"data-slot\": \"dropdown-menu-radio-group\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuRadioItem({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        \"data-slot\": \"dropdown-menu-radio-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"size-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuLabel({ className, inset, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        \"data-slot\": \"dropdown-menu-label\",\n        \"data-inset\": inset,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuSeparator({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        \"data-slot\": \"dropdown-menu-separator\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-border -mx-1 my-1 h-px\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuShortcut({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        \"data-slot\": \"dropdown-menu-shortcut\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground ml-auto text-xs tracking-widest\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuSub({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub, {\n        \"data-slot\": \"dropdown-menu-sub\",\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 198,\n        columnNumber: 10\n    }, this);\n}\nfunction DropdownMenuSubTrigger({ className, inset, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        \"data-slot\": \"dropdown-menu-sub-trigger\",\n        \"data-inset\": inset,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"ml-auto size-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuSubContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        \"data-slot\": \"dropdown-menu-sub-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Input({ className, type, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        \"data-slot\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\nfunction Label({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFFdkI7QUFFaEMsU0FBU0csTUFBTSxFQUNiQyxTQUFTLEVBQ1QsR0FBR0MsT0FDOEM7SUFDakQscUJBQ0UsOERBQUNKLHVEQUFtQjtRQUNsQk0sYUFBVTtRQUNWSCxXQUFXRiw4Q0FBRUEsQ0FDWCx1TkFDQUU7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVnQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTa2xsaVxcRGVza3RvcFxccXVpa3Jzb2x1dGlvbnNfcGxhdGZvcm1cXHF1aWtyc29sdXRpb25zLWFwcFxcc3JjXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBMYWJlbCh7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290Pikge1xuICByZXR1cm4gKFxuICAgIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgICBkYXRhLXNsb3Q9XCJsYWJlbFwiXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gbGVhZGluZy1ub25lIGZvbnQtbWVkaXVtIHNlbGVjdC1ub25lIGdyb3VwLWRhdGEtW2Rpc2FibGVkPXRydWVdOnBvaW50ZXItZXZlbnRzLW5vbmUgZ3JvdXAtZGF0YS1bZGlzYWJsZWQ9dHJ1ZV06b3BhY2l0eS01MCBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cblxuZXhwb3J0IHsgTGFiZWwgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjbiIsIkxhYmVsIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGF0YS1zbG90Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/navigation-menu.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/navigation-menu.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationMenu: () => (/* binding */ NavigationMenu),\n/* harmony export */   NavigationMenuContent: () => (/* binding */ NavigationMenuContent),\n/* harmony export */   NavigationMenuIndicator: () => (/* binding */ NavigationMenuIndicator),\n/* harmony export */   NavigationMenuItem: () => (/* binding */ NavigationMenuItem),\n/* harmony export */   NavigationMenuLink: () => (/* binding */ NavigationMenuLink),\n/* harmony export */   NavigationMenuList: () => (/* binding */ NavigationMenuList),\n/* harmony export */   NavigationMenuTrigger: () => (/* binding */ NavigationMenuTrigger),\n/* harmony export */   NavigationMenuViewport: () => (/* binding */ NavigationMenuViewport),\n/* harmony export */   navigationMenuTriggerStyle: () => (/* binding */ navigationMenuTriggerStyle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-navigation-menu */ \"(ssr)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\n\nfunction NavigationMenu({ className, children, viewport = true, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        \"data-slot\": \"navigation-menu\",\n        \"data-viewport\": viewport,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\", className),\n        ...props,\n        children: [\n            children,\n            viewport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationMenuViewport, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n                lineNumber: 27,\n                columnNumber: 20\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\nfunction NavigationMenuList({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.List, {\n        \"data-slot\": \"navigation-menu-list\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group flex flex-1 list-none items-center justify-center gap-1\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\nfunction NavigationMenuItem({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Item, {\n        \"data-slot\": \"navigation-menu-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nconst navigationMenuTriggerStyle = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\");\nfunction NavigationMenuTrigger({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n        \"data-slot\": \"navigation-menu-trigger\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(navigationMenuTriggerStyle(), \"group\", className),\n        ...props,\n        children: [\n            children,\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\nfunction NavigationMenuContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Content, {\n        \"data-slot\": \"navigation-menu-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\", \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\nfunction NavigationMenuViewport({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute top-full left-0 isolate z-50 flex justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n            \"data-slot\": \"navigation-menu-viewport\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\nfunction NavigationMenuLink({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Link, {\n        \"data-slot\": \"navigation-menu-link\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\nfunction NavigationMenuIndicator({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_navigation_menu__WEBPACK_IMPORTED_MODULE_4__.Indicator, {\n        \"data-slot\": \"navigation-menu-indicator\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\navigation-menu.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/navigation-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/popover.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/popover.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Popover: () => (/* binding */ Popover),\n/* harmony export */   PopoverContent: () => (/* binding */ PopoverContent),\n/* harmony export */   PopoverTrigger: () => (/* binding */ PopoverTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_popover__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popover */ \"(ssr)/./node_modules/@radix-ui/react-popover/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Popover,PopoverTrigger,PopoverContent auto */ \n\n\n\nconst Popover = _radix_ui_react_popover__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst PopoverTrigger = _radix_ui_react_popover__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst PopoverContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, align = \"center\", sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_popover__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_popover__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            align: align,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\popover.tsx\",\n            lineNumber: 17,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\popover.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nPopoverContent.displayName = _radix_ui_react_popover__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/popover.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/sonner.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/sonner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        style: {\n            \"--normal-bg\": \"var(--popover)\",\n            \"--normal-text\": \"var(--popover-foreground)\",\n            \"--normal-border\": \"var(--border)\"\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9zb25uZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVzQztBQUNrQjtBQUV4RCxNQUFNQyxVQUFVLENBQUMsRUFBRSxHQUFHRSxPQUFxQjtJQUN6QyxNQUFNLEVBQUVDLFFBQVEsUUFBUSxFQUFFLEdBQUdKLHFEQUFRQTtJQUVyQyxxQkFDRSw4REFBQ0UsMkNBQU1BO1FBQ0xFLE9BQU9BO1FBQ1BDLFdBQVU7UUFDVkMsT0FDRTtZQUNFLGVBQWU7WUFDZixpQkFBaUI7WUFDakIsbUJBQW1CO1FBQ3JCO1FBRUQsR0FBR0gsS0FBSzs7Ozs7O0FBR2Y7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcU2tsbGlcXERlc2t0b3BcXHF1aWtyc29sdXRpb25zX3BsYXRmb3JtXFxxdWlrcnNvbHV0aW9ucy1hcHBcXHNyY1xcY29tcG9uZW50c1xcdWlcXHNvbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuaW1wb3J0IHsgVG9hc3RlciBhcyBTb25uZXIsIFRvYXN0ZXJQcm9wcyB9IGZyb20gXCJzb25uZXJcIlxuXG5jb25zdCBUb2FzdGVyID0gKHsgLi4ucHJvcHMgfTogVG9hc3RlclByb3BzKSA9PiB7XG4gIGNvbnN0IHsgdGhlbWUgPSBcInN5c3RlbVwiIH0gPSB1c2VUaGVtZSgpXG5cbiAgcmV0dXJuIChcbiAgICA8U29ubmVyXG4gICAgICB0aGVtZT17dGhlbWUgYXMgVG9hc3RlclByb3BzW1widGhlbWVcIl19XG4gICAgICBjbGFzc05hbWU9XCJ0b2FzdGVyIGdyb3VwXCJcbiAgICAgIHN0eWxlPXtcbiAgICAgICAge1xuICAgICAgICAgIFwiLS1ub3JtYWwtYmdcIjogXCJ2YXIoLS1wb3BvdmVyKVwiLFxuICAgICAgICAgIFwiLS1ub3JtYWwtdGV4dFwiOiBcInZhcigtLXBvcG92ZXItZm9yZWdyb3VuZClcIixcbiAgICAgICAgICBcIi0tbm9ybWFsLWJvcmRlclwiOiBcInZhcigtLWJvcmRlcilcIixcbiAgICAgICAgfSBhcyBSZWFjdC5DU1NQcm9wZXJ0aWVzXG4gICAgICB9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBUb2FzdGVyIH1cbiJdLCJuYW1lcyI6WyJ1c2VUaGVtZSIsIlRvYXN0ZXIiLCJTb25uZXIiLCJwcm9wcyIsInRoZW1lIiwiY2xhc3NOYW1lIiwic3R5bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/use-toast.ts":
/*!****************************************!*\
  !*** ./src/components/ui/use-toast.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case actionTypes.ADD_TOAST:\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case actionTypes.UPDATE_TOAST:\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case actionTypes.DISMISS_TOAST:\n            {\n                const { toastId } = action;\n                // Doing this as the `dismiss` action can be called on a toast that's already been removed.\n                if (toastId) {\n                    return {\n                        ...state,\n                        toasts: state.toasts.map((t)=>t.id === toastId ? {\n                                ...t,\n                                open: false\n                            } : t)\n                    };\n                } else {\n                    return {\n                        ...state,\n                        toasts: state.toasts.map((t)=>({\n                                ...t,\n                                open: false\n                            }))\n                    };\n                }\n            }\n        case actionTypes.REMOVE_TOAST:\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n        default:\n            return state;\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>listener(memoryState));\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: actionTypes.UPDATE_TOAST,\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: actionTypes.DISMISS_TOAST,\n            toastId: id\n        });\n    dispatch({\n        type: actionTypes.ADD_TOAST,\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) {\n                    dismiss();\n                }\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: actionTypes.DISMISS_TOAST,\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./src/contexts/authcontext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/authcontext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n// src/contexts/AuthContext.tsx\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n // Our browser client\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createSupabaseBrowserClient)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setIsLoading(true);\n            const { data: authListener } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    console.log('AuthContext: onAuthStateChange event:', event, 'session:', session); // <<< ADD THIS LOG\n                    setSession(session);\n                    setUser(session?.user ?? null);\n                    setIsLoading(false); // Ensure loading is set to false after session update\n                // If you want an immediate client-side redirect after login/signup from here,\n                // you could add it, but middleware is generally preferred for consistency.\n                // For example:\n                // if (event === 'SIGNED_IN' && window.location.pathname.startsWith('/login') || window.location.pathname.startsWith('/signup')) {\n                //    console.log('AuthContext: SIGNED_IN detected, attempting to redirect to /dashboard');\n                //    router.push('/dashboard'); // Requires useRouter from 'next/navigation'\n                // }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Get initial session (this runs on mount)\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    try {\n                        const { data, error: sessionError } = await supabase.auth.getSession();\n                        if (sessionError) throw sessionError;\n                        console.log('AuthContext: Initial session fetched:', data.session); // <<< ADD THIS LOG\n                        setSession(data.session);\n                        setUser(data.session?.user ?? null);\n                    } catch (e) {\n                        console.error(\"AuthContext: Error fetching initial session:\", e);\n                    // Removed setError(e) as setError is not defined\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    // Corrected unsubscribe call based on Supabase documentation\n                    authListener?.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        supabase\n    ]); // Add supabase.auth to dependency array\n    const signOut = async ()=>{\n        setIsLoading(true);\n        const { error } = await supabase.auth.signOut();\n        if (error) {\n            console.error('Error signing out:', error.message);\n        // Potentially show a toast notification with Sonner\n        // toast.error(`Sign out failed: ${error.message}`);\n        } else {\n            setUser(null);\n            setSession(null);\n        // toast.success('Signed out successfully');\n        }\n        setIsLoading(false);\n    };\n    const value = {\n        user,\n        session,\n        isLoading,\n        signOut\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\src\\\\contexts\\\\authcontext.tsx\",\n        lineNumber: 93,\n        columnNumber: 10\n    }, undefined);\n};\n// Custom hook to use the AuthContext\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvYXV0aGNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSwrQkFBK0I7O0FBRzBEO0FBRXJCLENBQUMscUJBQXFCO0FBUzFGLE1BQU1NLDRCQUFjTCxvREFBYUEsQ0FBOEJNO0FBTXhELE1BQU1DLGVBQWUsQ0FBQyxFQUFFQyxRQUFRLEVBQXFCO0lBQzFELE1BQU1DLFdBQVdMLGlGQUEyQkE7SUFDNUMsTUFBTSxDQUFDTSxNQUFNQyxRQUFRLEdBQUdSLCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ1MsU0FBU0MsV0FBVyxHQUFHViwrQ0FBUUEsQ0FBaUI7SUFDdkQsTUFBTSxDQUFDVyxXQUFXQyxhQUFhLEdBQUdaLCtDQUFRQSxDQUFDO0lBRTNDRCxnREFBU0E7a0NBQUM7WUFDUmEsYUFBYTtZQUViLE1BQU0sRUFBRUMsTUFBTUMsWUFBWSxFQUFFLEdBQUdSLFNBQVNTLElBQUksQ0FBQ0MsaUJBQWlCOzBDQUM1RCxPQUFPQyxPQUFPUjtvQkFDWlMsUUFBUUMsR0FBRyxDQUFDLHlDQUF5Q0YsT0FBTyxZQUFZUixVQUFVLG1CQUFtQjtvQkFDckdDLFdBQVdEO29CQUNYRCxRQUFRQyxTQUFTRixRQUFRO29CQUN6QkssYUFBYSxRQUFRLHNEQUFzRDtnQkFFM0UsOEVBQThFO2dCQUM5RSwyRUFBMkU7Z0JBQzNFLGVBQWU7Z0JBQ2Ysa0lBQWtJO2dCQUNsSSwyRkFBMkY7Z0JBQzNGLDZFQUE2RTtnQkFDN0UsSUFBSTtnQkFDTjs7WUFHRiwyQ0FBMkM7WUFDM0MsTUFBTVE7NERBQW9CO29CQUN4QixJQUFJO3dCQUNGLE1BQU0sRUFBRVAsSUFBSSxFQUFFUSxPQUFPQyxZQUFZLEVBQUUsR0FBRyxNQUFNaEIsU0FBU1MsSUFBSSxDQUFDUSxVQUFVO3dCQUNwRSxJQUFJRCxjQUFjLE1BQU1BO3dCQUN4QkosUUFBUUMsR0FBRyxDQUFDLHlDQUF5Q04sS0FBS0osT0FBTyxHQUFHLG1CQUFtQjt3QkFDdkZDLFdBQVdHLEtBQUtKLE9BQU87d0JBQ3ZCRCxRQUFRSyxLQUFLSixPQUFPLEVBQUVGLFFBQVE7b0JBQ2hDLEVBQUUsT0FBT2lCLEdBQUc7d0JBQ1ZOLFFBQVFHLEtBQUssQ0FBQyxnREFBZ0RHO29CQUM5RCxpREFBaUQ7b0JBQ25ELFNBQVU7d0JBQ1JaLGFBQWE7b0JBQ2Y7Z0JBQ0Y7O1lBRUFRO1lBRUE7MENBQU87b0JBQ0wsNkRBQTZEO29CQUM3RE4sY0FBY1csYUFBYUM7Z0JBQzdCOztRQUNGO2lDQUFHO1FBQUNwQjtLQUFTLEdBQUcsd0NBQXdDO0lBRXhELE1BQU1xQixVQUFVO1FBQ2RmLGFBQWE7UUFDYixNQUFNLEVBQUVTLEtBQUssRUFBRSxHQUFHLE1BQU1mLFNBQVNTLElBQUksQ0FBQ1ksT0FBTztRQUM3QyxJQUFJTixPQUFPO1lBQ1RILFFBQVFHLEtBQUssQ0FBQyxzQkFBc0JBLE1BQU1PLE9BQU87UUFDakQsb0RBQW9EO1FBQ3BELG9EQUFvRDtRQUN0RCxPQUFPO1lBQ0xwQixRQUFRO1lBQ1JFLFdBQVc7UUFDWCw0Q0FBNEM7UUFDOUM7UUFDQUUsYUFBYTtJQUNmO0lBRUEsTUFBTWlCLFFBQVE7UUFDWnRCO1FBQ0FFO1FBQ0FFO1FBQ0FnQjtJQUNGO0lBRUEscUJBQU8sOERBQUN6QixZQUFZNEIsUUFBUTtRQUFDRCxPQUFPQTtrQkFBUXhCOzs7Ozs7QUFDOUMsRUFBRTtBQUVGLHFDQUFxQztBQUM5QixNQUFNMEIsVUFBVTtJQUNyQixNQUFNQyxVQUFVbEMsaURBQVVBLENBQUNJO0lBQzNCLElBQUk4QixZQUFZN0IsV0FBVztRQUN6QixNQUFNLElBQUk4QixNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNrbGxpXFxEZXNrdG9wXFxxdWlrcnNvbHV0aW9uc19wbGF0Zm9ybVxccXVpa3Jzb2x1dGlvbnMtYXBwXFxzcmNcXGNvbnRleHRzXFxhdXRoY29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2NvbnRleHRzL0F1dGhDb250ZXh0LnRzeFxyXG4ndXNlIGNsaWVudCc7IC8vIFRoaXMgY29udGV4dCB3aWxsIGJlIHVzZWQgaW4gY2xpZW50IGNvbXBvbmVudHNcclxuXHJcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlLCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IFNlc3Npb24sIFVzZXIgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnO1xyXG5pbXBvcnQgeyBjcmVhdGVTdXBhYmFzZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAL2xpYi9zdXBhYmFzZS9jbGllbnQnOyAvLyBPdXIgYnJvd3NlciBjbGllbnRcclxuXHJcbnR5cGUgQXV0aENvbnRleHRUeXBlID0ge1xyXG4gIHVzZXI6IFVzZXIgfCBudWxsO1xyXG4gIHNlc3Npb246IFNlc3Npb24gfCBudWxsO1xyXG4gIGlzTG9hZGluZzogYm9vbGVhbjtcclxuICBzaWduT3V0OiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xyXG59O1xyXG5cclxuY29uc3QgQXV0aENvbnRleHQgPSBjcmVhdGVDb250ZXh0PEF1dGhDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcclxuXHJcbmludGVyZmFjZSBBdXRoUHJvdmlkZXJQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IEF1dGhQcm92aWRlciA9ICh7IGNoaWxkcmVuIH06IEF1dGhQcm92aWRlclByb3BzKSA9PiB7XHJcbiAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTdXBhYmFzZUJyb3dzZXJDbGllbnQoKTtcclxuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW3Nlc3Npb24sIHNldFNlc3Npb25dID0gdXNlU3RhdGU8U2Vzc2lvbiB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldElzTG9hZGluZyh0cnVlKTtcclxuXHJcbiAgICBjb25zdCB7IGRhdGE6IGF1dGhMaXN0ZW5lciB9ID0gc3VwYWJhc2UuYXV0aC5vbkF1dGhTdGF0ZUNoYW5nZShcclxuICAgICAgYXN5bmMgKGV2ZW50LCBzZXNzaW9uKSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0F1dGhDb250ZXh0OiBvbkF1dGhTdGF0ZUNoYW5nZSBldmVudDonLCBldmVudCwgJ3Nlc3Npb246Jywgc2Vzc2lvbik7IC8vIDw8PCBBREQgVEhJUyBMT0dcclxuICAgICAgICBzZXRTZXNzaW9uKHNlc3Npb24pO1xyXG4gICAgICAgIHNldFVzZXIoc2Vzc2lvbj8udXNlciA/PyBudWxsKTtcclxuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpOyAvLyBFbnN1cmUgbG9hZGluZyBpcyBzZXQgdG8gZmFsc2UgYWZ0ZXIgc2Vzc2lvbiB1cGRhdGVcclxuXHJcbiAgICAgICAgLy8gSWYgeW91IHdhbnQgYW4gaW1tZWRpYXRlIGNsaWVudC1zaWRlIHJlZGlyZWN0IGFmdGVyIGxvZ2luL3NpZ251cCBmcm9tIGhlcmUsXHJcbiAgICAgICAgLy8geW91IGNvdWxkIGFkZCBpdCwgYnV0IG1pZGRsZXdhcmUgaXMgZ2VuZXJhbGx5IHByZWZlcnJlZCBmb3IgY29uc2lzdGVuY3kuXHJcbiAgICAgICAgLy8gRm9yIGV4YW1wbGU6XHJcbiAgICAgICAgLy8gaWYgKGV2ZW50ID09PSAnU0lHTkVEX0lOJyAmJiB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUuc3RhcnRzV2l0aCgnL2xvZ2luJykgfHwgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9zaWdudXAnKSkge1xyXG4gICAgICAgIC8vICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogU0lHTkVEX0lOIGRldGVjdGVkLCBhdHRlbXB0aW5nIHRvIHJlZGlyZWN0IHRvIC9kYXNoYm9hcmQnKTtcclxuICAgICAgICAvLyAgICByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZCcpOyAvLyBSZXF1aXJlcyB1c2VSb3V0ZXIgZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xyXG4gICAgICAgIC8vIH1cclxuICAgICAgfVxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBHZXQgaW5pdGlhbCBzZXNzaW9uICh0aGlzIHJ1bnMgb24gbW91bnQpXHJcbiAgICBjb25zdCBnZXRJbml0aWFsU2Vzc2lvbiA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCB7IGRhdGEsIGVycm9yOiBzZXNzaW9uRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0U2Vzc2lvbigpO1xyXG4gICAgICAgIGlmIChzZXNzaW9uRXJyb3IpIHRocm93IHNlc3Npb25FcnJvcjtcclxuICAgICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IEluaXRpYWwgc2Vzc2lvbiBmZXRjaGVkOicsIGRhdGEuc2Vzc2lvbik7IC8vIDw8PCBBREQgVEhJUyBMT0dcclxuICAgICAgICBzZXRTZXNzaW9uKGRhdGEuc2Vzc2lvbik7XHJcbiAgICAgICAgc2V0VXNlcihkYXRhLnNlc3Npb24/LnVzZXIgPz8gbnVsbCk7XHJcbiAgICAgIH0gY2F0Y2ggKGUpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiQXV0aENvbnRleHQ6IEVycm9yIGZldGNoaW5nIGluaXRpYWwgc2Vzc2lvbjpcIiwgZSk7XHJcbiAgICAgICAgLy8gUmVtb3ZlZCBzZXRFcnJvcihlKSBhcyBzZXRFcnJvciBpcyBub3QgZGVmaW5lZFxyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgZ2V0SW5pdGlhbFNlc3Npb24oKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICAvLyBDb3JyZWN0ZWQgdW5zdWJzY3JpYmUgY2FsbCBiYXNlZCBvbiBTdXBhYmFzZSBkb2N1bWVudGF0aW9uXHJcbiAgICAgIGF1dGhMaXN0ZW5lcj8uc3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKCk7XHJcbiAgICB9O1xyXG4gIH0sIFtzdXBhYmFzZV0pOyAvLyBBZGQgc3VwYWJhc2UuYXV0aCB0byBkZXBlbmRlbmN5IGFycmF5XHJcblxyXG4gIGNvbnN0IHNpZ25PdXQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XHJcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKTtcclxuICAgIGlmIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzaWduaW5nIG91dDonLCBlcnJvci5tZXNzYWdlKTtcclxuICAgICAgLy8gUG90ZW50aWFsbHkgc2hvdyBhIHRvYXN0IG5vdGlmaWNhdGlvbiB3aXRoIFNvbm5lclxyXG4gICAgICAvLyB0b2FzdC5lcnJvcihgU2lnbiBvdXQgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgICBzZXRTZXNzaW9uKG51bGwpO1xyXG4gICAgICAvLyB0b2FzdC5zdWNjZXNzKCdTaWduZWQgb3V0IHN1Y2Nlc3NmdWxseScpO1xyXG4gICAgfVxyXG4gICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCB2YWx1ZSA9IHtcclxuICAgIHVzZXIsXHJcbiAgICBzZXNzaW9uLFxyXG4gICAgaXNMb2FkaW5nLFxyXG4gICAgc2lnbk91dCxcclxuICB9O1xyXG5cclxuICByZXR1cm4gPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+e2NoaWxkcmVufTwvQXV0aENvbnRleHQuUHJvdmlkZXI+O1xyXG59O1xyXG5cclxuLy8gQ3VzdG9tIGhvb2sgdG8gdXNlIHRoZSBBdXRoQ29udGV4dFxyXG5leHBvcnQgY29uc3QgdXNlQXV0aCA9ICgpOiBBdXRoQ29udGV4dFR5cGUgPT4ge1xyXG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcclxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZUF1dGggbXVzdCBiZSB1c2VkIHdpdGhpbiBhbiBBdXRoUHJvdmlkZXInKTtcclxuICB9XHJcbiAgcmV0dXJuIGNvbnRleHQ7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJjcmVhdGVTdXBhYmFzZUJyb3dzZXJDbGllbnQiLCJBdXRoQ29udGV4dCIsInVuZGVmaW5lZCIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwic3VwYWJhc2UiLCJ1c2VyIiwic2V0VXNlciIsInNlc3Npb24iLCJzZXRTZXNzaW9uIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZGF0YSIsImF1dGhMaXN0ZW5lciIsImF1dGgiLCJvbkF1dGhTdGF0ZUNoYW5nZSIsImV2ZW50IiwiY29uc29sZSIsImxvZyIsImdldEluaXRpYWxTZXNzaW9uIiwiZXJyb3IiLCJzZXNzaW9uRXJyb3IiLCJnZXRTZXNzaW9uIiwiZSIsInN1YnNjcmlwdGlvbiIsInVuc3Vic2NyaWJlIiwic2lnbk91dCIsIm1lc3NhZ2UiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwidXNlQXV0aCIsImNvbnRleHQiLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/authcontext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseBrowserClient: () => (/* binding */ createSupabaseBrowserClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n// src/lib/supabase/client.ts\n\nfunction createSupabaseBrowserClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://ivjlbjxbnmxgbwsiryys.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml2amxianhibm14Z2J3c2lyeXlzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4MjM3ODUsImV4cCI6MjA2MzM5OTc4NX0.HTi6esUoEI86zk-Tdd8EdX5SRVxA9UcKmoTKpJyy78c\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLDZCQUE2QjtBQUN1QjtBQUU3QyxTQUFTQztJQUNkLE9BQU9ELGtFQUFtQkEsQ0FDeEJFLDBDQUFvQyxFQUNwQ0Esa05BQXlDO0FBRTdDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNrbGxpXFxEZXNrdG9wXFxxdWlrcnNvbHV0aW9uc19wbGF0Zm9ybVxccXVpa3Jzb2x1dGlvbnMtYXBwXFxzcmNcXGxpYlxcc3VwYWJhc2VcXGNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvbGliL3N1cGFiYXNlL2NsaWVudC50c1xyXG5pbXBvcnQgeyBjcmVhdGVCcm93c2VyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3Nzcic7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlU3VwYWJhc2VCcm93c2VyQ2xpZW50KCkge1xyXG4gIHJldHVybiBjcmVhdGVCcm93c2VyQ2xpZW50KFxyXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcclxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiY3JlYXRlQnJvd3NlckNsaWVudCIsImNyZWF0ZVN1cGFiYXNlQnJvd3NlckNsaWVudCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTa2xsaVxcRGVza3RvcFxccXVpa3Jzb2x1dGlvbnNfcGxhdGZvcm1cXHF1aWtyc29sdXRpb25zLWFwcFxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/@floating-ui","vendor-chunks/sonner","vendor-chunks/whatwg-url","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/lucide-react","vendor-chunks/cookie","vendor-chunks/next-themes","vendor-chunks/aria-hidden","vendor-chunks/class-variance-authority","vendor-chunks/react-remove-scroll-bar","vendor-chunks/webidl-conversions","vendor-chunks/use-sync-external-store","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/clsx","vendor-chunks/get-nonce","vendor-chunks/geist","vendor-chunks/zod","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();