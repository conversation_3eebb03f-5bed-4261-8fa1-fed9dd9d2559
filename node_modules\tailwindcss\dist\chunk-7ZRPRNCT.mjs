function f(n){if(arguments.length==0)throw new TypeError("`CSS.escape` requires an argument.");var e=String(n),r=e.length,i=-1,t,s="",l=e.charCodeAt(0);if(r==1&&l==45)return"\\"+e;for(;++i<r;){if(t=e.charCodeAt(i),t==0){s+="\uFFFD";continue}if(t>=1&&t<=31||t==127||i==0&&t>=48&&t<=57||i==1&&t>=48&&t<=57&&l==45){s+="\\"+t.toString(16)+" ";continue}if(t>=128||t==45||t==95||t>=48&&t<=57||t>=65&&t<=90||t>=97&&t<=122){s+=e.charAt(i);continue}s+="\\"+e.charAt(i)}return s}function c(n){return n.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,e=>e.length>2?String.fromCodePoint(Number.parseInt(e.slice(1).trim(),16)):e[1])}var p=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-underline-offset","--text-indent","--text-decoration-thickness","--text-decoration-color"]]]);function h(n,e){return(p.get(e)??[]).some(r=>n===r||n.startsWith(`${r}-`))}var g=class{constructor(e=new Map,r=new Set([])){this.values=e;this.keyframes=r}prefix=null;add(e,r,i=0){if(e.endsWith("\\*")&&(e=e.slice(0,-2)+"*"),e.endsWith("-*")){if(r!=="initial")throw new Error(`Invalid theme value \`${r}\` for namespace \`${e}\``);e==="--*"?this.values.clear():this.clearNamespace(e.slice(0,-2),0)}if(i&4){let t=this.values.get(e);if(t&&!(t.options&4))return}r==="initial"?this.values.delete(e):this.values.set(e,{value:r,options:i})}keysInNamespaces(e){let r=[];for(let i of e){let t=`${i}-`;for(let s of this.values.keys())s.startsWith(t)&&s.indexOf("--",2)===-1&&(h(s,i)||r.push(s.slice(t.length)))}return r}get(e){for(let r of e){let i=this.values.get(r);if(i)return i.value}return null}hasDefault(e){return(this.getOptions(e)&4)===4}getOptions(e){return this.values.get(e)?.options??0}entries(){return this.prefix?Array.from(this.values,e=>(e[0]=this.#i(e[0]),e)):this.values.entries()}#i(e){return this.prefix?`--${this.prefix}-${e.slice(2)}`:e}clearNamespace(e,r){let i=p.get(e)??[];e:for(let t of this.values.keys())if(t.startsWith(e)){if(r!==0&&(this.getOptions(t)&r)!==r)continue;for(let s of i)if(t.startsWith(s))continue e;this.values.delete(t)}}#e(e,r){for(let i of r){let t=e!==null?f(`${i}-${e.replaceAll(".","_")}`):i;if(this.values.has(t)&&!h(t,i))return t}return null}#t(e){return this.values.has(e)?`var(${this.#i(e)})`:null}resolve(e,r){let i=this.#e(e,r);if(!i)return null;let t=this.values.get(i);return t.options&1?t.value:this.#t(i)}resolveValue(e,r){let i=this.#e(e,r);return i?this.values.get(i).value:null}resolveWith(e,r,i=[]){let t=this.#e(e,r);if(!t)return null;let s={};for(let u of i){let o=`${t}${u}`,a=this.values.get(o);a&&(a.options&1?s[u]=a.value:s[u]=this.#t(o))}let l=this.values.get(t);return l.options&1?[l.value,s]:[this.#t(t),s]}namespace(e){let r=new Map,i=`${e}-`;for(let[t,s]of this.values)t===e?r.set(null,s.value):t.startsWith(`${i}-`)?r.set(t.slice(e.length),s.value):t.startsWith(i)&&r.set(t.slice(i.length),s.value);return r}addKeyframes(e){this.keyframes.add(e)}getKeyframes(){return Array.from(this.keyframes)}};export{f as a,c as b,g as c};
