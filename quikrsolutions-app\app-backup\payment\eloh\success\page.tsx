"use client";

import { useSearchParams } from 'next/navigation';
import RealtimeTransaction from '@/components/RealtimeTransaction';
import { Suspense, useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from 'next/link';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { Database } from '@/types/supabase';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";


function SuccessContent() {
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId') || '';
  const userId = searchParams.get('userId') || '';
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [transaction, setTransaction] = useState<Database['public']['Tables']['eloh_crypto_transactions']['Row'] | null>(null);
  const supabase = createSupabaseBrowserClient();

  useEffect(() => {
    if (!orderId) return;

    const fetchInitialTransaction = async () => {
      const { data, error } = await supabase
        .from('eloh_crypto_transactions')
        .select('*')
        .eq('quikrsolutions_order_id', orderId)
        .single();

      if (error) {
        console.error('Error fetching initial transaction:', error);
      } else {
        setTransaction(data);
        if (data.status === 'confirmed') {
          setIsConfirmed(true);
        }
      }
    };

    fetchInitialTransaction();

    const channel = supabase
      .channel(`eloh_crypto_transactions_${orderId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'eloh_crypto_transactions',
          filter: `quikrsolutions_order_id=eq.${orderId}`,
        },
        (payload: { new: Database['public']['Tables']['eloh_crypto_transactions']['Row'] }) => {
          console.log('Received Realtime update on success page:', payload);
          const updatedTransaction = payload.new;
          setTransaction(updatedTransaction);
          if (updatedTransaction.status === 'confirmed') {
            setIsConfirmed(true);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [orderId, supabase]);


  if (!orderId || !userId) {
    return (
      <div className="container mx-auto mt-8 px-4">
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Order ID or User ID missing from redirect. Please return to the subscriptions page.
          </AlertDescription>
        </Alert>
        <div className="mt-4">
          <Link href="/subscriptions" passHref>
            <Button>Go to Subscriptions</Button>
          </Link>
        </div>
      </div>
    );
  }


  return (
    <div className="container mx-auto mt-8 px-4">
      <Card>
        <CardHeader>
          <CardTitle>Payment Status</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">Thank you for your payment! We are processing your crypto transaction.</p>
          <RealtimeTransaction orderId={orderId} userId={userId} />
          {isConfirmed ? (
            <div className="mt-6">
              <p className="mb-2">Your payment has been confirmed.</p>
              <Link href="/dashboard" passHref>
                <Button>Go to Dashboard</Button>
              </Link>
            </div>
          ) : (
            <p className="mt-4 text-sm text-gray-500">
              Updates are live. Please keep this page open to see the final status.
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default function ElohPaymentSuccessPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto mt-8 px-4">
        <Card>
          <CardHeader>
            <CardTitle>Loading Payment Status...</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Please wait while we load your transaction details.</p>
          </CardContent>
        </Card>
      </div>
    }>
      <SuccessContent />
    </Suspense>
  );
}
