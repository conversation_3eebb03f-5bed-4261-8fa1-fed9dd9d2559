{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,iEAAiE;AACjE,+DAA+D;AAC/D,qDAAqD;AACrD,4DAA4D;AAC5D,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAA;AACtC,OAAO,EAAE,OAAO,EAAE,CAAA;AAQlB,MAAM,SAAS,GAAG,CAAC,OAAY,EAAwB,EAAE,CACvD,CAAC,CAAC,OAAO;IACT,OAAO,OAAO,KAAK,QAAQ;IAC3B,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU;IAC5C,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU;IAClC,OAAO,OAAO,CAAC,UAAU,KAAK,UAAU;IACxC,OAAO,OAAO,CAAC,SAAS,KAAK,UAAU;IACvC,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU;IAClC,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ;IAC/B,OAAO,OAAO,CAAC,EAAE,KAAK,UAAU,CAAA;AAElC,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;AACtD,MAAM,MAAM,GAAqD,UAAU,CAAA;AAC3E,MAAM,oBAAoB,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;AAwB/D,2BAA2B;AAC3B,MAAM,OAAO;IACX,OAAO,GAAY;QACjB,SAAS,EAAE,KAAK;QAChB,IAAI,EAAE,KAAK;KACZ,CAAA;IAED,SAAS,GAAc;QACrB,SAAS,EAAE,EAAE;QACb,IAAI,EAAE,EAAE;KACT,CAAA;IAED,KAAK,GAAW,CAAC,CAAA;IACjB,EAAE,GAAW,IAAI,CAAC,MAAM,EAAE,CAAA;IAE1B;QACE,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE;YACxB,OAAO,MAAM,CAAC,YAAY,CAAC,CAAA;SAC5B;QACD,oBAAoB,CAAC,MAAM,EAAE,YAAY,EAAE;YACzC,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE,KAAK;SACpB,CAAC,CAAA;IACJ,CAAC;IAED,EAAE,CAAC,EAAa,EAAE,EAAW;QAC3B,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IAC7B,CAAC;IAED,cAAc,CAAC,EAAa,EAAE,EAAW;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;QAC/B,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QAC1B,qBAAqB;QACrB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACZ,OAAM;SACP;QACD,oBAAoB;QACpB,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;SAChB;aAAM;YACL,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;SAClB;IACH,CAAC;IAED,IAAI,CACF,EAAa,EACb,IAA+B,EAC/B,MAA6B;QAE7B,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACpB,OAAO,KAAK,CAAA;SACb;QACD,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;QACvB,IAAI,GAAG,GAAY,KAAK,CAAA;QACxB,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;YACnC,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,IAAI,GAAG,CAAA;SACvC;QACD,IAAI,EAAE,KAAK,MAAM,EAAE;YACjB,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,CAAA;SAClD;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;CACF;AAED,MAAe,cAAc;CAI5B;AAED,MAAM,cAAc,GAAG,CAA2B,OAAU,EAAE,EAAE;IAC9D,OAAO;QACL,MAAM,CAAC,EAAW,EAAE,IAA+B;YACjD,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACjC,CAAC;QACD,IAAI;YACF,OAAO,OAAO,CAAC,IAAI,EAAE,CAAA;QACvB,CAAC;QACD,MAAM;YACJ,OAAO,OAAO,CAAC,MAAM,EAAE,CAAA;QACzB,CAAC;KACF,CAAA;AACH,CAAC,CAAA;AAED,MAAM,kBAAmB,SAAQ,cAAc;IAC7C,MAAM;QACJ,OAAO,GAAG,EAAE,GAAE,CAAC,CAAA;IACjB,CAAC;IACD,IAAI,KAAI,CAAC;IACT,MAAM,KAAI,CAAC;CACZ;AAED,MAAM,UAAW,SAAQ,cAAc;IACrC,gDAAgD;IAChD,oCAAoC;IACpC,qBAAqB;IACrB,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAA;IAC5D,oBAAoB;IACpB,QAAQ,GAAG,IAAI,OAAO,EAAE,CAAA;IACxB,QAAQ,CAAW;IACnB,oBAAoB,CAAmB;IACvC,0BAA0B,CAAyB;IAEnD,aAAa,GAA2C,EAAE,CAAA;IAC1D,OAAO,GAAY,KAAK,CAAA;IAExB,YAAY,OAAkB;QAC5B,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;QACvB,mCAAmC;QACnC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAA;QACvB,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;YACzB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE;gBAC7B,sDAAsD;gBACtD,uDAAuD;gBACvD,qDAAqD;gBACrD,mBAAmB;gBACnB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;gBAC9C,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAA;gBAC7B,mEAAmE;gBACnE,oEAAoE;gBACpE,kEAAkE;gBAClE,kEAAkE;gBAClE,iEAAiE;gBACjE,WAAW;gBACX,qBAAqB;gBACrB,MAAM,CAAC,GAAG,OAET,CAAA;gBACD,IACE,OAAO,CAAC,CAAC,uBAAuB,KAAK,QAAQ;oBAC7C,OAAO,CAAC,CAAC,uBAAuB,CAAC,KAAK,KAAK,QAAQ,EACnD;oBACA,KAAK,IAAI,CAAC,CAAC,uBAAuB,CAAC,KAAK,CAAA;iBACzC;gBACD,oBAAoB;gBACpB,IAAI,SAAS,CAAC,MAAM,KAAK,KAAK,EAAE;oBAC9B,IAAI,CAAC,MAAM,EAAE,CAAA;oBACb,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,CAAA;oBACjD,qBAAqB;oBACrB,MAAM,CAAC,GAAG,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAA;oBAC/C,IAAI,CAAC,GAAG;wBAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;oBACtC,oBAAoB;iBACrB;YACH,CAAC,CAAA;SACF;QAED,IAAI,CAAC,0BAA0B,GAAG,OAAO,CAAC,UAAU,CAAA;QACpD,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,IAAI,CAAA;IAC1C,CAAC;IAED,MAAM,CAAC,EAAW,EAAE,IAA+B;QACjD,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC7B,OAAO,GAAG,EAAE,GAAE,CAAC,CAAA;SAChB;QACD,oBAAoB;QAEpB,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YAC1B,IAAI,CAAC,IAAI,EAAE,CAAA;SACZ;QAED,MAAM,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAA;QAClD,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QACxB,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;YACpC,IACE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;gBAC5C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,CAAC,EACjD;gBACA,IAAI,CAAC,MAAM,EAAE,CAAA;aACd;QACH,CAAC,CAAA;IACH,CAAC;IAED,IAAI;QACF,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAM;SACP;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QAEnB,yDAAyD;QACzD,4DAA4D;QAC5D,4DAA4D;QAC5D,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAA;QAExB,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;YACzB,IAAI;gBACF,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;gBAClC,IAAI,EAAE;oBAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;aAClC;YAAC,OAAO,CAAC,EAAE,GAAE;SACf;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAU,EAAE,GAAG,CAAQ,EAAE,EAAE;YAC/C,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAA;QACpC,CAAC,CAAA;QACD,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,IAAgC,EAAE,EAAE;YAC9D,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;QACtC,CAAC,CAAA;IACH,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAM;SACP;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QAEpB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YACxC,qBAAqB;YACrB,IAAI,CAAC,QAAQ,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,GAAG,CAAC,CAAA;aAC3D;YACD,oBAAoB;YACpB,IAAI;gBACF,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;gBAC3C,qBAAqB;aACtB;YAAC,OAAO,CAAC,EAAE,GAAE;YACd,oBAAoB;QACtB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAA;QAC9C,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,0BAA0B,CAAA;QAC1D,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED,kBAAkB,CAAC,IAAgC;QACjD,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC7B,OAAO,CAAC,CAAA;SACT;QACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,CAAA;QAClC,oBAAoB;QAEpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACxD,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,CACzC,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACvB,CAAA;IACH,CAAC;IAED,YAAY,CAAC,EAAU,EAAE,GAAG,IAAW;QACrC,MAAM,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAA;QACpC,IAAI,EAAE,KAAK,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC7C,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;gBAC/B,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;gBAChC,qBAAqB;aACtB;YACD,qBAAqB;YACrB,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;YAC/C,qBAAqB;YACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;YACxD,oBAAoB;YACpB,OAAO,GAAG,CAAA;SACX;aAAM;YACL,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;SAC3C;IACH,CAAC;CACF;AAED,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAA;AAClC,iEAAiE;AACjE,yBAAyB;AACzB,MAAM,CAAC,MAAM;AACX;;;;;;;;GAQG;AACH,MAAM;AAEN;;;;;;GAMG;AACH,IAAI;AAEJ;;;;;;GAMG;AACH,MAAM,GACP,GAAG,cAAc,CAChB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,kBAAkB,EAAE,CACxE,CAAA", "sourcesContent": ["// Note: since nyc uses this module to output coverage, any lines\n// that are in the direct sync flow of nyc's outputCoverage are\n// ignored, since we can never get coverage for them.\n// grab a reference to node's real process object right away\nimport { signals } from './signals.js'\nexport { signals }\n\n// just a loosened process type so we can do some evil things\ntype ProcessRE = NodeJS.Process & {\n  reallyExit: (code?: number | undefined | null) => any\n  emit: (ev: string, ...a: any[]) => any\n}\n\nconst processOk = (process: any): process is ProcessRE =>\n  !!process &&\n  typeof process === 'object' &&\n  typeof process.removeListener === 'function' &&\n  typeof process.emit === 'function' &&\n  typeof process.reallyExit === 'function' &&\n  typeof process.listeners === 'function' &&\n  typeof process.kill === 'function' &&\n  typeof process.pid === 'number' &&\n  typeof process.on === 'function'\n\nconst kExitEmitter = Symbol.for('signal-exit emitter')\nconst global: typeof globalThis & { [kExitEmitter]?: Emitter } = globalThis\nconst ObjectDefineProperty = Object.defineProperty.bind(Object)\n\n/**\n * A function that takes an exit code and signal as arguments\n *\n * In the case of signal exits *only*, a return value of true\n * will indicate that the signal is being handled, and we should\n * not synthetically exit with the signal we received. Regardless\n * of the handler return value, the handler is unloaded when an\n * otherwise fatal signal is received, so you get exactly 1 shot\n * at it, unless you add another onExit handler at that point.\n *\n * In the case of numeric code exits, we may already have committed\n * to exiting the process, for example via a fatal exception or\n * unhandled promise rejection, so it is impossible to stop safely.\n */\nexport type Handler = (\n  code: number | null | undefined,\n  signal: NodeJS.Signals | null\n) => true | void\ntype ExitEvent = 'afterExit' | 'exit'\ntype Emitted = { [k in ExitEvent]: boolean }\ntype Listeners = { [k in ExitEvent]: Handler[] }\n\n// teeny special purpose ee\nclass Emitter {\n  emitted: Emitted = {\n    afterExit: false,\n    exit: false,\n  }\n\n  listeners: Listeners = {\n    afterExit: [],\n    exit: [],\n  }\n\n  count: number = 0\n  id: number = Math.random()\n\n  constructor() {\n    if (global[kExitEmitter]) {\n      return global[kExitEmitter]\n    }\n    ObjectDefineProperty(global, kExitEmitter, {\n      value: this,\n      writable: false,\n      enumerable: false,\n      configurable: false,\n    })\n  }\n\n  on(ev: ExitEvent, fn: Handler) {\n    this.listeners[ev].push(fn)\n  }\n\n  removeListener(ev: ExitEvent, fn: Handler) {\n    const list = this.listeners[ev]\n    const i = list.indexOf(fn)\n    /* c8 ignore start */\n    if (i === -1) {\n      return\n    }\n    /* c8 ignore stop */\n    if (i === 0 && list.length === 1) {\n      list.length = 0\n    } else {\n      list.splice(i, 1)\n    }\n  }\n\n  emit(\n    ev: ExitEvent,\n    code: number | null | undefined,\n    signal: NodeJS.Signals | null\n  ): boolean {\n    if (this.emitted[ev]) {\n      return false\n    }\n    this.emitted[ev] = true\n    let ret: boolean = false\n    for (const fn of this.listeners[ev]) {\n      ret = fn(code, signal) === true || ret\n    }\n    if (ev === 'exit') {\n      ret = this.emit('afterExit', code, signal) || ret\n    }\n    return ret\n  }\n}\n\nabstract class SignalExitBase {\n  abstract onExit(cb: Handler, opts?: { alwaysLast?: boolean }): () => void\n  abstract load(): void\n  abstract unload(): void\n}\n\nconst signalExitWrap = <T extends SignalExitBase>(handler: T) => {\n  return {\n    onExit(cb: Handler, opts?: { alwaysLast?: boolean }) {\n      return handler.onExit(cb, opts)\n    },\n    load() {\n      return handler.load()\n    },\n    unload() {\n      return handler.unload()\n    },\n  }\n}\n\nclass SignalExitFallback extends SignalExitBase {\n  onExit() {\n    return () => {}\n  }\n  load() {}\n  unload() {}\n}\n\nclass SignalExit extends SignalExitBase {\n  // \"SIGHUP\" throws an `ENOSYS` error on Windows,\n  // so use a supported signal instead\n  /* c8 ignore start */\n  #hupSig = process.platform === 'win32' ? 'SIGINT' : 'SIGHUP'\n  /* c8 ignore stop */\n  #emitter = new Emitter()\n  #process: ProcessRE\n  #originalProcessEmit: ProcessRE['emit']\n  #originalProcessReallyExit: ProcessRE['reallyExit']\n\n  #sigListeners: { [k in NodeJS.Signals]?: () => void } = {}\n  #loaded: boolean = false\n\n  constructor(process: ProcessRE) {\n    super()\n    this.#process = process\n    // { <signal>: <listener fn>, ... }\n    this.#sigListeners = {}\n    for (const sig of signals) {\n      this.#sigListeners[sig] = () => {\n        // If there are no other listeners, an exit is coming!\n        // Simplest way: remove us and then re-send the signal.\n        // We know that this will kill the process, so we can\n        // safely emit now.\n        const listeners = this.#process.listeners(sig)\n        let { count } = this.#emitter\n        // This is a workaround for the fact that signal-exit v3 and signal\n        // exit v4 are not aware of each other, and each will attempt to let\n        // the other handle it, so neither of them do. To correct this, we\n        // detect if we're the only handler *except* for previous versions\n        // of signal-exit, and increment by the count of listeners it has\n        // created.\n        /* c8 ignore start */\n        const p = process as unknown as {\n          __signal_exit_emitter__?: { count: number }\n        }\n        if (\n          typeof p.__signal_exit_emitter__ === 'object' &&\n          typeof p.__signal_exit_emitter__.count === 'number'\n        ) {\n          count += p.__signal_exit_emitter__.count\n        }\n        /* c8 ignore stop */\n        if (listeners.length === count) {\n          this.unload()\n          const ret = this.#emitter.emit('exit', null, sig)\n          /* c8 ignore start */\n          const s = sig === 'SIGHUP' ? this.#hupSig : sig\n          if (!ret) process.kill(process.pid, s)\n          /* c8 ignore stop */\n        }\n      }\n    }\n\n    this.#originalProcessReallyExit = process.reallyExit\n    this.#originalProcessEmit = process.emit\n  }\n\n  onExit(cb: Handler, opts?: { alwaysLast?: boolean }) {\n    /* c8 ignore start */\n    if (!processOk(this.#process)) {\n      return () => {}\n    }\n    /* c8 ignore stop */\n\n    if (this.#loaded === false) {\n      this.load()\n    }\n\n    const ev = opts?.alwaysLast ? 'afterExit' : 'exit'\n    this.#emitter.on(ev, cb)\n    return () => {\n      this.#emitter.removeListener(ev, cb)\n      if (\n        this.#emitter.listeners['exit'].length === 0 &&\n        this.#emitter.listeners['afterExit'].length === 0\n      ) {\n        this.unload()\n      }\n    }\n  }\n\n  load() {\n    if (this.#loaded) {\n      return\n    }\n    this.#loaded = true\n\n    // This is the number of onSignalExit's that are in play.\n    // It's important so that we can count the correct number of\n    // listeners on signals, and don't wait for the other one to\n    // handle it instead of us.\n    this.#emitter.count += 1\n\n    for (const sig of signals) {\n      try {\n        const fn = this.#sigListeners[sig]\n        if (fn) this.#process.on(sig, fn)\n      } catch (_) {}\n    }\n\n    this.#process.emit = (ev: string, ...a: any[]) => {\n      return this.#processEmit(ev, ...a)\n    }\n    this.#process.reallyExit = (code?: number | null | undefined) => {\n      return this.#processReallyExit(code)\n    }\n  }\n\n  unload() {\n    if (!this.#loaded) {\n      return\n    }\n    this.#loaded = false\n\n    signals.forEach(sig => {\n      const listener = this.#sigListeners[sig]\n      /* c8 ignore start */\n      if (!listener) {\n        throw new Error('Listener not defined for signal: ' + sig)\n      }\n      /* c8 ignore stop */\n      try {\n        this.#process.removeListener(sig, listener)\n        /* c8 ignore start */\n      } catch (_) {}\n      /* c8 ignore stop */\n    })\n    this.#process.emit = this.#originalProcessEmit\n    this.#process.reallyExit = this.#originalProcessReallyExit\n    this.#emitter.count -= 1\n  }\n\n  #processReallyExit(code?: number | null | undefined) {\n    /* c8 ignore start */\n    if (!processOk(this.#process)) {\n      return 0\n    }\n    this.#process.exitCode = code || 0\n    /* c8 ignore stop */\n\n    this.#emitter.emit('exit', this.#process.exitCode, null)\n    return this.#originalProcessReallyExit.call(\n      this.#process,\n      this.#process.exitCode\n    )\n  }\n\n  #processEmit(ev: string, ...args: any[]): any {\n    const og = this.#originalProcessEmit\n    if (ev === 'exit' && processOk(this.#process)) {\n      if (typeof args[0] === 'number') {\n        this.#process.exitCode = args[0]\n        /* c8 ignore start */\n      }\n      /* c8 ignore start */\n      const ret = og.call(this.#process, ev, ...args)\n      /* c8 ignore start */\n      this.#emitter.emit('exit', this.#process.exitCode, null)\n      /* c8 ignore stop */\n      return ret\n    } else {\n      return og.call(this.#process, ev, ...args)\n    }\n  }\n}\n\nconst process = globalThis.process\n// wrap so that we call the method on the actual handler, without\n// exporting it directly.\nexport const {\n  /**\n   * Called when the process is exiting, whether via signal, explicit\n   * exit, or running out of stuff to do.\n   *\n   * If the global process object is not suitable for instrumentation,\n   * then this will be a no-op.\n   *\n   * Returns a function that may be used to unload signal-exit.\n   */\n  onExit,\n\n  /**\n   * Load the listeners.  Likely you never need to call this, unless\n   * doing a rather deep integration with signal-exit functionality.\n   * Mostly exposed for the benefit of testing.\n   *\n   * @internal\n   */\n  load,\n\n  /**\n   * Unload the listeners.  Likely you never need to call this, unless\n   * doing a rather deep integration with signal-exit functionality.\n   * Mostly exposed for the benefit of testing.\n   *\n   * @internal\n   */\n  unload,\n} = signalExitWrap(\n  processOk(process) ? new SignalExit(process) : new SignalExitFallback()\n)\n"]}