'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useRealtimeRemittance } from '@/hooks/useRealtimeRemittance';
import { Database } from '@/types/supabase';
import { Loader2, Search, CheckCircle, Clock, AlertCircle, XCircle, ArrowRight } from 'lucide-react';

type RemittanceTransactionRow = Database['public']['Tables']['remittance_transactions']['Row'];


export default function RemittanceTrackingPage() {
  const searchParams = useSearchParams();
  const urlRemittanceId = searchParams.get('id');

  const [remittanceId, setRemittanceId] = useState(urlRemittanceId || '');
  const [remittanceDetails, setRemittanceDetails] = useState<RemittanceTransactionRow | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [inputId, setInputId] = useState(urlRemittanceId || '');

  // Use the realtime hook for live updates
  const { remittanceDetails: realtimeUpdate } = useRealtimeRemittance(remittanceId);


  // Fetch remittance details when ID changes
  const fetchRemittanceDetails = async (id: string) => {
    if (!id.trim()) {
      setError('Please enter a remittance ID');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/quikr/remittances/details?id=${encodeURIComponent(id)}`);
      const result = await response.json();

      if (response.ok && result.status === 'success') {
        setRemittanceDetails(result.data);
        setRemittanceId(id);
      } else {
        setError(result.message || 'Failed to fetch remittance details');
        setRemittanceDetails(null);
      }
    } catch (err: any) {
      setError('Failed to fetch remittance details');
      console.error('Error fetching remittance details:', err);
      setRemittanceDetails(null);
    } finally {
      setLoading(false);
    }
  };

  // Auto-fetch if ID is provided in URL
  useEffect(() => {
    if (urlRemittanceId) {
      fetchRemittanceDetails(urlRemittanceId);
    }
  }, [urlRemittanceId]);

  // Update details when realtime update comes in
  useEffect(() => {
    if (realtimeUpdate && remittanceDetails) {
      setRemittanceDetails(prev => prev ? { ...prev, ...realtimeUpdate } : realtimeUpdate);
    }
  }, [realtimeUpdate, remittanceDetails]);


  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'processing': case 'in_transit': return <Clock className="h-5 w-5 text-blue-500" />;
      case 'pending': case 'funded': return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'failed': case 'cancelled': return <XCircle className="h-5 w-5 text-red-500" />;
      default: return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'default';
      case 'processing': case 'in_transit': return 'secondary';
      case 'pending': case 'funded': return 'outline';
      case 'failed': case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  const handleSearch = () => {
    fetchRemittanceDetails(inputId);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Track Your Remittance</h1>
        <p className="text-muted-foreground">Enter your remittance ID to track its status</p>
      </div>

      {/* Search Section */}
      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="flex gap-2">
            <Input
              placeholder="Enter remittance ID"
              value={inputId}
              onChange={(e) => setInputId(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
            />
            <Button onClick={handleSearch} disabled={loading || !inputId.trim()}>
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
            </Button>
          </div>
          {error && (
            <p className="text-sm text-red-500 mt-2">{error}</p>
          )}
        </CardContent>
      </Card>

      {/* Results Section */}
      {remittanceDetails && (
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(remittanceDetails.status)}
              Remittance Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Status */}
            <div className="flex items-center justify-between">
              <span className="font-medium">Status:</span>
              <Badge variant={getStatusBadgeVariant(remittanceDetails.status)}>
                {remittanceDetails.status}
              </Badge>
            </div>

            {/* Transaction Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <h3 className="font-semibold">Transaction Details</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Remittance ID:</span>
                    <span className="font-mono">{remittanceDetails.quikrsolutions_remittance_id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Send Amount:</span>
                    <span>{remittanceDetails.send_amount} {remittanceDetails.send_currency}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Receive Amount:</span>
                    <span>{remittanceDetails.receive_amount} {remittanceDetails.receive_currency}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Exchange Rate:</span>
                    <span>{remittanceDetails.exchange_rate}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fee:</span>
                    <span>{remittanceDetails.fee} {remittanceDetails.send_currency}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold">Recipient Details</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Name:</span>
                    <span>{remittanceDetails.recipient_first_name} {remittanceDetails.recipient_last_name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Country:</span>
                    <span>{remittanceDetails.recipient_country}</span>
                  </div>
                  {remittanceDetails.recipient_phone_number && (
                    <div className="flex justify-between">
                      <span>Phone:</span>
                      <span>{remittanceDetails.recipient_phone_number}</span>
                    </div>
                  )}
                  {remittanceDetails.recipient_email && (
                    <div className="flex justify-between">
                      <span>Email:</span>
                      <span>{remittanceDetails.recipient_email}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Timeline */}
            <div className="space-y-3">
              <h3 className="font-semibold">Timeline</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-3 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Remittance initiated</span>
                  <span className="text-muted-foreground ml-auto">
                    {new Date(remittanceDetails.created_at).toLocaleString()}
                  </span>
                </div>
                {remittanceDetails.status !== 'pending' && (
                  <div className="flex items-center gap-3 text-sm">
                    <ArrowRight className="h-4 w-4 text-blue-500" />
                    <span>Processing started</span>
                  </div>
                )}
                {remittanceDetails.status === 'completed' && (
                  <div className="flex items-center gap-3 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Remittance completed</span>
                  </div>
                )}
              </div>
            </div>

            {/* Real-time indicator */}
            {realtimeUpdate && (
              <div className="text-xs text-muted-foreground text-center">
                ✨ Status updates in real-time
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
