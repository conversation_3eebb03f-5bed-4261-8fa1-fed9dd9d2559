{"name": "@nowpaymentsio/nowpayments-api-js", "version": "1.0.5", "description": "A library for interacting with the NOWPayments API", "author": "bogdanaks <<EMAIL>> (https://github.com/bogdanaks)", "main": "./dist/nowpayments-api-js.js", "types": "./src/index.d.ts", "keywords": ["Accept Bitcoin payments", "Accept crypto", "Accept crypto payments", "Accept crypto payments online", "Accept crypto on Shopify", "Accept crypto donations", "ADA", "ADA crypto", "Aeternity", "AeternumToken", "ALGO", "ARK", "AVA", "Atomic Wallet", "Basic Attention Token", "<PERSON><PERSON>", "Bitcoin", "Bitcoin payment", "Bitcoin payment gateway", "Bitcoin payment processor", "BlockChain", "BNB", "BNB Coin", "Binance Coin", "Bitcoin (Lightning Network)", "Bitcoin Cash", "BCH", "business", "Cardano", "Cardano ADA", "Coin", "Cryptocurrency", "Cryptocurrencies", "Crypto", "Crypto payment", "Crypto payment gateway", "Crypto payment processor", "Crypto plugin", "Crypto WooCommerce", "Dash", "DAO", "DAO Maker", "DGB", "Digibyte", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Donate with crypto", "e<PERSON><PERSON>d", "ecwid ecommerce", "Elrond Network", "EGLD", "Euro", "ERC-20", "Ecommerce", "Ecommerce solutions", "ETH", "Ethereum", "Ethereum payment processor", "EOS crypto", "<PERSON><PERSON>", "Ether Classic", "ETC", "Ethereum Classic", "<PERSON><PERSON>", "Fantom token", "Fiat", "Filecoin", "finance", "FunFair FUN", "Gaming", "Gambling", "HBAR", "<PERSON><PERSON><PERSON>", "Ho<PERSON><PERSON>", "Horizen ZEN", "Litecoin", "Maker DAO", "<PERSON><PERSON>", "<PERSON><PERSON>", "NEO", "OKB", "OKEx", "Payments", "Payment gateway", "Payment processor crypto", "Payment solutions", "<PERSON><PERSON>t", "PrestaShop", "Ravencoin", "RVN", "R<PERSON>ple crypto", "Shopify", "Shopify crypto", "Shopify crypto payments", "<PERSON><PERSON>", "Tether USD", "Token", "Tron", "TRX", "TrueUSD", "United States Dollar", "USD", "USDT", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Verge", "Wallet", "WAVES", "WooCommerce", "WooCommerce Crypto", "Wordpress crypto plugins", "XMR", "XRP", "XVG", "ZEC", "ZenCart", "Zcash", "ZCash ZEC", "ZEN"], "repository": {"type": "git", "url": "https://github.com/NowPaymentsIO/nowpayments-api-js.git"}, "bugs": "https://github.com/NowPaymentsIO/nowpayments-api-js/issues", "private": false, "scripts": {"build": "NODE_ENV=production grunt build", "start": "NODE_ENV=development && webpack-cli --watch --mode development", "eslint:fix": "eslint --fix ./src"}, "license": "MIT", "dependencies": {"axios": "^0.21.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.21.0", "eslint": "^7.23.0", "eslint-config-standard-with-typescript": "^20.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "grunt": "^1.3.0", "grunt-cli": "^1.4.2", "grunt-contrib-clean": "^2.0.0", "grunt-eslint": "^23.0.0", "grunt-ts": "^6.0.0-beta.22", "grunt-webpack": "^4.0.2", "load-grunt-tasks": "^5.1.0", "terser-webpack-plugin": "^5.1.1", "ts-loader": "^8.1.0", "ts-node": "^9.1.1", "tslint": "^6.1.3", "typescript": "^4.2.3", "uglifyjs-webpack-plugin": "2.2.0", "webpack": "^5.30.0", "webpack-cli": "^4.6.0"}}