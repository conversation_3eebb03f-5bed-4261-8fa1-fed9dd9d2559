'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import PayWithCryptoButton from '@/components/subscriptions/PayWithCryptoButton';
import { SubscribeButton } from '@/components/subscriptions/SubscribeButton';
import RealtimeTransaction from '@/components/RealtimeTransaction';
import { Database } from '@/types/supabase'; // Import your database types

interface SubscriptionClientContentProps {
  user: Database['public']['Tables']['profiles']['Row']; // Assuming user type from profiles table
  plans: { name: string; price: string; priceId: string; description: string }[];
}

const SubscriptionClientContent: React.FC<SubscriptionClientContentProps> = ({ user, plans }) => {
  const [orderId, setOrderId] = useState<string | null>(null);

  return (
    <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
      {plans.map((plan) => (
        <Card key={plan.priceId} style={{ minHeight: '200px' }}>
          <CardHeader>
            <CardTitle>{plan.name}</CardTitle>
            <CardDescription>{plan.price}</CardDescription>
          </CardHeader>
          <CardContent>
            <p>{plan.description}</p>
          </CardContent>
          <CardFooter style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <SubscribeButton priceId={plan.priceId} planName={plan.name + " (Credit/Debit Card)"} />
            <br />
            <div>
              <PayWithCryptoButton
                amount={parseFloat(plan.price.replace("$", "").replace("/month", ""))}
                currency={"USD"}
                planId={plan.priceId}
                userId={user.id}
                onOrderCreated={(id) => {
                  console.log("Order ID created:", id);
                  setOrderId(id);
                }}
              />
              {/* Render RealtimeTransaction only if orderId is available */}
              {orderId && <RealtimeTransaction orderId={orderId} userId={user.id} />}
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

export default SubscriptionClientContent;
