// src/contexts/AuthContext.tsx
'use client'; // This context will be used in client components

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { createSupabaseBrowserClient } from '@/lib/supabase/client'; // Our browser client

type AuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const supabase = createSupabaseBrowserClient();
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('AuthContext: onAuthStateChange event:', event, 'session:', session); // <<< ADD THIS LOG
        setSession(session);
        setUser(session?.user ?? null);
        setIsLoading(false); // Ensure loading is set to false after session update

        // If you want an immediate client-side redirect after login/signup from here,
        // you could add it, but middleware is generally preferred for consistency.
        // For example:
        // if (event === 'SIGNED_IN' && window.location.pathname.startsWith('/login') || window.location.pathname.startsWith('/signup')) {
        //    console.log('AuthContext: SIGNED_IN detected, attempting to redirect to /dashboard');
        //    router.push('/dashboard'); // Requires useRouter from 'next/navigation'
        // }
      }
    );

    // Get initial session (this runs on mount)
    const getInitialSession = async () => {
      try {
        const { data, error: sessionError } = await supabase.auth.getSession();
        if (sessionError) throw sessionError;
        console.log('AuthContext: Initial session fetched:', data.session); // <<< ADD THIS LOG
        setSession(data.session);
        setUser(data.session?.user ?? null);
      } catch (e) {
        console.error("AuthContext: Error fetching initial session:", e);
        // Removed setError(e) as setError is not defined
      } finally {
        setIsLoading(false);
      }
    };

    getInitialSession();

    return () => {
      // Corrected unsubscribe call based on Supabase documentation
      authListener?.subscription.unsubscribe();
    };
  }, [supabase]); // Add supabase.auth to dependency array

  const signOut = async () => {
    setIsLoading(true);
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Error signing out:', error.message);
      // Potentially show a toast notification with Sonner
      // toast.error(`Sign out failed: ${error.message}`);
    } else {
      setUser(null);
      setSession(null);
      // toast.success('Signed out successfully');
    }
    setIsLoading(false);
  };

  const value = {
    user,
    session,
    isLoading,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the AuthContext
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
