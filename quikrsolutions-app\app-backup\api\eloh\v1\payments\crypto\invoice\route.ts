import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { initiateCryptoPayment } from '@/lib/services/eloh/elohCryptoService';
import { InitiateElohCryptoPaymentParams } from '@/lib/services/eloh/elohTypes';

const APP_URL = process.env.NEXT_PUBLIC_APP_URL;

if (!APP_URL) {
  console.error("FATAL ERROR: Missing required App URL environment variable.");
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { quikrUserId, amountFiat, currencyFiat, description, orderId } = body;

    if (!quikrUserId || !amountFiat || !currencyFiat) {
      return new NextResponse(JSON.stringify({ error: 'Missing required fields' }), { status: 400 });
    }

    const quikrsolutionsOrderId = orderId || uuidv4();

    const paymentParams: InitiateElohCryptoPaymentParams = {
      amountFiat: amountFiat,
      fiatCurrency: currencyFiat,
      description: description || `Payment for user ${quikrUserId}`,
      quikrsolutionsOrderId: quikrsolutionsOrderId,
      // Assuming buyerEmail is not passed in the current body, add if needed
      // buyerEmail: body.buyerEmail,
      redirectUrl: `${APP_URL}/payment/eloh/success?quikrsolutionsOrderId=${quikrsolutionsOrderId}`, // Updated redirect URL
      notificationUrl: `${APP_URL}/api/webhooks/eloh/btcpay`, // Webhook URL
    };

    const result = await initiateCryptoPayment(paymentParams);

    if (result.status === 'success') {
      return new NextResponse(
        JSON.stringify({
          elohOrderId: result.data.elohTransactionId, // Using Eloh's transaction ID
          btcpayInvoiceId: result.data.btcpayInvoiceId,
          btcpayCheckoutLink: result.data.btcpayInvoiceUrl, // Using Eloh's invoice URL
        }),
        { status: 201, headers: { 'Content-Type': 'application/json' } }
      );
    } else {
      console.error('Error from Eloh service:', result.message, result.details);
      return new NextResponse(
        JSON.stringify({ error: result.message }),
        { status: 500 } // Or map specific Eloh error codes to HTTP status codes
      );
    }
  } catch (error: any) {
    console.error('Error creating invoice:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), { status: 500 });
  }
}
