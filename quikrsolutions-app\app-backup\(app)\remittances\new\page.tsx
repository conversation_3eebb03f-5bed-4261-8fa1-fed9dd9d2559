'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react'; // Assuming lucide-react is installed

// Define the form schema using Zod (based on InitiateElohRemittanceParams)
const formSchema = z.object({
  sendAmount: z.number().positive({ message: "Amount must be positive." }),
  sendCurrency: z.string().min(1, { message: "Select a send currency." }),
  receiveCurrency: z.string().min(1, { message: "Select a receive currency." }),
  recipientFirstName: z.string().min(1, { message: "Recipient first name is required." }),
  recipientLastName: z.string().min(1, { message: "Recipient last name is required." }),
  recipientCountry: z.string().min(1, { message: "Select a recipient country." }),
  recipientPhoneNumber: z.string().optional(), // Optional for now
  recipientEmail: z.string().email({ message: "Invalid email address." }).optional().or(z.literal('')), // Optional or empty string
  recipientStellarAddress: z.string().optional(), // Optional for now
  recipientBankName: z.string().optional(), // Optional for now
  recipientBankAccountNumber: z.string().optional(), // Optional for now
  recipientPickupLocationId: z.string().optional(), // Optional for now
  purposeOfRemittance: z.string().optional(), // Optional
  // Add quoteId to the schema as optional, as it's added before submission
  quoteId: z.string().optional(),
});

type RemittanceFormValues = z.infer<typeof formSchema>;

// Mock data for currencies and countries - replace with actual data fetching
const mockCurrencies = ['USD', 'CAD', 'GBP', 'EUR', 'XCD', 'JMD', 'GHS'];
const mockCountries = ['USA', 'Canada', 'UK', 'Dominica', 'Jamaica', 'Ghana'];

export default function NewRemittancePage() {
  const router = useRouter();
  const { toast } = useToast();

  const [quote, setQuote] = useState<any | null>(null); // TODO: Define a proper type for quote
  const [isFetchingQuote, setIsFetchingQuote] = useState(false);
  const [quoteError, setQuoteError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);


  const form = useForm<RemittanceFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sendAmount: 0,
      sendCurrency: '',
      receiveCurrency: '',
      recipientFirstName: '',
      recipientLastName: '',
      recipientCountry: '',
      recipientPhoneNumber: '',
      recipientEmail: '',
      recipientStellarAddress: '',
      recipientBankName: '',
      recipientBankAccountNumber: '',
      recipientPickupLocationId: '',
      purposeOfRemittance: '',
    },
  });

  const { watch, trigger } = form;
  const watchedFields = watch(['sendAmount', 'sendCurrency', 'receiveCurrency']);

  // Effect to fetch quote when relevant fields change and are valid
  useEffect(() => {
    const fetchQuote = async () => {
      const [sendAmount, sendCurrency, receiveCurrency] = watchedFields;

      // Only fetch if all required fields for quote are filled and valid
      if (sendAmount > 0 && sendCurrency && receiveCurrency) {
         const isValid = await trigger(['sendAmount', 'sendCurrency', 'receiveCurrency']);
         if (!isValid) return;

        setIsFetchingQuote(true);
        setQuoteError(null);
        setQuote(null);

        try {
          const response = await fetch(`/api/quikr/remittances/rates?fromCurrency=${sendCurrency}&toCurrency=${receiveCurrency}&amount=${sendAmount}`);

          // Log the response status and body for debugging
          console.log('Quote API Response Status:', response.status);
          const result = await response.json();
          console.log('Quote API Response Body:', result);


          if (response.ok) {
            // Check if the response has the expected structure
            if (result.status === 'success' && result.data) {
               // Log the properties of result.data for debugging
               console.log('Quote Data Properties:', Object.keys(result.data));
               console.log('Quote Data Content:', result.data);

               setQuote(result.data);
            } else {
               // Handle unexpected successful response structure
               const errorMessage = result.message || 'Unexpected response structure from quote API.';
               setQuoteError(errorMessage);
               toast({
                  title: 'Error fetching quote',
                  description: errorMessage,
                  variant: 'destructive',
               });
               console.error('Unexpected quote API response:', result);
            }
          } else {
            // Handle non-OK response status
            const errorMessage = result.message || `Failed to fetch exchange rate. Status: ${response.status}`;
            setQuoteError(errorMessage);
            toast({
              title: 'Error fetching quote',
              description: errorMessage,
              variant: 'destructive',
            });
             console.error('Quote API returned non-OK status:', response.status, result);
          }
        } catch (error: any) {
          setQuoteError('An unexpected error occurred while fetching the exchange rate.');
           toast({
              title: 'Error fetching quote',
              description: 'An unexpected error occurred.',
              variant: 'destructive',
            });
          console.error('Error fetching quote:', error);
        } finally {
          setIsFetchingQuote(false);
        }
      } else {
        // Clear quote if fields are not filled
        setQuote(null);
        setQuoteError(null);
      }
    };

    // Debounce fetching the quote slightly to avoid excessive API calls
    const handler = setTimeout(() => {
      fetchQuote();
    }, 500);

    return () => {
      clearTimeout(handler);
    };

  }, [watchedFields[0], watchedFields[1], watchedFields[2], trigger, toast]); // Depend on watched fields and trigger


  async function onSubmit(values: RemittanceFormValues) {
    setIsSubmitting(true);
    setQuoteError(null); // Clear any previous quote errors

    // Re-fetch quote immediately before submitting to ensure it's not expired
     if (!quote || new Date() > new Date(quote.expiresAt)) {
         await trigger(['sendAmount', 'sendCurrency', 'receiveCurrency']); // Re-validate quote fields
         // Attempt to fetch a new quote
         await fetch(`/api/quikr/remittances/rates?fromCurrency=${values.sendCurrency}&toCurrency=${values.receiveCurrency}&amount=${values.sendAmount}`)
            .then(res => res.json())
            .then(result => {
                if (result.status === 'success') {
                    setQuote(result.data);
                    // Use the new quoteId for submission
                    values.quoteId = result.data.elohQuoteId; // Update values with new quoteId
                } else {
                    setQuoteError(result.message || 'Failed to refresh quote before submission.');
                    toast({
                        title: 'Submission Failed',
                        description: result.message || 'Could not refresh exchange rate quote.',
                        variant: 'destructive',
                    });
                    setIsSubmitting(false);
                    return; // Stop submission if quote refresh fails
                }
            })
            .catch(error => {
                 setQuoteError('An unexpected error occurred while refreshing the quote.');
                 toast({
                    title: 'Submission Failed',
                    description: 'An unexpected error occurred during quote refresh.',
                    variant: 'destructive',
                });
                console.error('Error refreshing quote:', error);
                setIsSubmitting(false);
                return; // Stop submission if quote refresh fails
            });
     } else {
         // Use the existing valid quoteId for submission
         values.quoteId = quote.elohQuoteId;
     }

    // Ensure we have a valid quoteId before proceeding
    if (!values.quoteId) {
         toast({
            title: 'Submission Failed',
            description: quoteError || 'No valid quote available for submission.',
            variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
    }


    // Placeholder for userId - in a real app, get this from auth session
    const userId = 'placeholder-user-id'; // TODO: Get actual user ID

    try {
      const response = await fetch('/api/eloh/v1/remittances/stellar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values, // Include all form values
          userId: userId, // Add user ID
          // Ensure recipientName is split into first/last if needed by API, or adjust API
          recipientFirstName: values.recipientFirstName,
          recipientLastName: values.recipientLastName,
          // Ensure recipientAddress maps to correct field (e.g., recipientStellarAddress)
          recipientStellarAddress: values.recipientStellarAddress,
          // Ensure other optional fields are included if present in form values
        }),
      });

      const result = await response.json();

      if (response.ok) {
        console.log('Remittance initiated successfully:', result);
        toast({
          title: 'Remittance Initiated',
          description: 'Your remittance has been successfully initiated.',
        });
        // Redirect to tracking page or history page
        // router.push(`/remittances/track/${result.data.quikrsolutionsRemittanceId}`); // Assuming API returns this ID
         router.push('/remittances'); // Redirect to history for now
      } else {
        console.error('Error initiating remittance:', result.message, result.details);
        toast({
          title: 'Remittance Failed',
          description: result.message || 'An error occurred while initiating the remittance.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      console.error('Error submitting form:', error);
      toast({
        title: 'Submission Failed',
        description: 'An unexpected error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>New Remittance</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="sendAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount to Send</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="100"
                        {...field}
                        onChange={e => {
                          const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                          field.onChange(isNaN(value) ? 0 : value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sendCurrency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Send Currency</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockCurrencies.map((currency) => (
                          <SelectItem key={currency} value={currency}>
                            {currency}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="receiveCurrency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Receive Currency</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockCurrencies.map((currency) => (
                          <SelectItem key={currency} value={currency}>
                            {currency}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Exchange Rate Quote Display */}
              {isFetchingQuote && (
                <Alert>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <AlertDescription>Fetching exchange rate...</AlertDescription>
                </Alert>
              )}

              {quote && !isFetchingQuote && (
                <Alert>
                  <AlertDescription>
                    <div className="space-y-1">
                      <div>Rate: 1 {quote.fromCurrency} = {quote.rate} {quote.toCurrency}</div>
                      <div>You send: {quote.sourceAmount} {quote.fromCurrency}</div>
                      <div>Recipient gets: {quote.destinationAmount} {quote.toCurrency}</div>
                      <div>Fee: {quote.fee} {quote.fromCurrency}</div>
                      <div>Total charged: {quote.totalCharged} {quote.fromCurrency}</div>
                      <div className="text-xs text-muted-foreground">Quote expires: {new Date(quote.expiresAt).toLocaleString()}</div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {quoteError && (
                <Alert variant="destructive">
                  <AlertDescription>{quoteError}</AlertDescription>
                </Alert>
              )}

              <FormField
                control={form.control}
                name="recipientFirstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recipient First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="recipientLastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recipient Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="recipientCountry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recipient Country</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockCountries.map((country) => (
                          <SelectItem key={country} value={country}>
                            {country}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="recipientPhoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recipient Phone Number (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="+1234567890" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="recipientEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recipient Email (Optional)</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="purposeOfRemittance"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Purpose of Remittance (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Family support" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isSubmitting || isFetchingQuote || !quote}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Initiate Remittance
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
