import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { Database } from '@/types/supabase';

type RemittanceTransactionRow = Database['public']['Tables']['remittance_transactions']['Row'];

export interface RemittanceDetailsResponse {
  status: 'success';
  data: RemittanceTransactionRow;
}

export interface RemittanceDetailsErrorResponse {
  status: 'error';
  message: string;
  details?: Record<string, any>;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const remittanceId = searchParams.get('id');

    if (!remittanceId) {
      return NextResponse.json(
        { 
          status: 'error', 
          message: 'Remittance ID is required',
        } as RemittanceDetailsErrorResponse,
        { status: 400 }
      );
    }

    const supabase = createSupabaseServerClient();
    
    // For public tracking, we don't require authentication
    // But we could add optional auth to show more details for the sender
    
    // Fetch remittance details by quikrsolutions_remittance_id
    const { data: remittance, error: fetchError } = await supabase
      .from('remittance_transactions')
      .select('*')
      .eq('quikrsolutions_remittance_id', remittanceId)
      .single();

    if (fetchError) {
      console.error('Error fetching remittance details:', fetchError);
      
      if (fetchError.code === 'PGRST116') {
        // No rows returned
        return NextResponse.json(
          { 
            status: 'error', 
            message: 'Remittance not found',
            details: { remittanceId }
          } as RemittanceDetailsErrorResponse,
          { status: 404 }
        );
      }
      
      return NextResponse.json(
        { 
          status: 'error', 
          message: 'Failed to fetch remittance details',
          details: { fetchError: fetchError.message }
        } as RemittanceDetailsErrorResponse,
        { status: 500 }
      );
    }

    if (!remittance) {
      return NextResponse.json(
        { 
          status: 'error', 
          message: 'Remittance not found',
          details: { remittanceId }
        } as RemittanceDetailsErrorResponse,
        { status: 404 }
      );
    }

    return NextResponse.json({
      status: 'success',
      data: remittance
    } as RemittanceDetailsResponse);

  } catch (error: any) {
    console.error('Unexpected error in remittance details API:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'An unexpected error occurred',
        details: { error: error.message }
      } as RemittanceDetailsErrorResponse,
      { status: 500 }
    );
  }
}
