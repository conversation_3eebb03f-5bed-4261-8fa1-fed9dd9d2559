{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/contexts/authcontext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/authcontext.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/authcontext.tsx <module evaluation>\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/contexts/authcontext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/authcontext.tsx\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/authcontext.tsx\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,8OAAC,8KAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,8OAAC;;;;;;;;;;;AAGpB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,8KAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,8KAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,6BAA6B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACnC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,8KAAA,CAAA,UAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,8OAAC,wNAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,8KAAA,CAAA,UAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;kBAGF,cAAA,8OAAC,8KAAA,CAAA,WAAgC;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,8KAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ydACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,8OAAC,8KAAA,CAAA,YAAiC;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/avatar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx <module evaluation>\",\n    \"Avatar\",\n);\nexport const AvatarFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx <module evaluation>\",\n    \"AvatarFallback\",\n);\nexport const AvatarImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx <module evaluation>\",\n    \"AvatarImage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8DACA", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/avatar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx\",\n    \"Avatar\",\n);\nexport const AvatarFallback = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx\",\n    \"AvatarFallback\",\n);\nexport const AvatarImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/avatar.tsx\",\n    \"AvatarImage\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0CACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0CACA", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/dropdown-menu.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DropdownMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenu() from the server but DropdownMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenu\",\n);\nexport const DropdownMenuCheckboxItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuCheckboxItem() from the server but DropdownMenuCheckboxItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuCheckboxItem\",\n);\nexport const DropdownMenuContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuContent() from the server but DropdownMenuContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuContent\",\n);\nexport const DropdownMenuGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuGroup() from the server but DropdownMenuGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuGroup\",\n);\nexport const DropdownMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuItem() from the server but DropdownMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuItem\",\n);\nexport const DropdownMenuLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuLabel() from the server but DropdownMenuLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuLabel\",\n);\nexport const DropdownMenuPortal = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuPortal() from the server but DropdownMenuPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuPortal\",\n);\nexport const DropdownMenuRadioGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuRadioGroup() from the server but DropdownMenuRadioGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuRadioGroup\",\n);\nexport const DropdownMenuRadioItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuRadioItem() from the server but DropdownMenuRadioItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuRadioItem\",\n);\nexport const DropdownMenuSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuSeparator() from the server but DropdownMenuSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuSeparator\",\n);\nexport const DropdownMenuShortcut = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuShortcut() from the server but DropdownMenuShortcut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuShortcut\",\n);\nexport const DropdownMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuSub() from the server but DropdownMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuSub\",\n);\nexport const DropdownMenuSubContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuSubContent() from the server but DropdownMenuSubContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuSubContent\",\n);\nexport const DropdownMenuSubTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuSubTrigger() from the server but DropdownMenuSubTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuSubTrigger\",\n);\nexport const DropdownMenuTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuTrigger() from the server but DropdownMenuTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx <module evaluation>\",\n    \"DropdownMenuTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qEACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,qEACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,qEACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,qEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,qEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,qEACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,qEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qEACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,qEACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,qEACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,qEACA", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/dropdown-menu.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DropdownMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenu() from the server but DropdownMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenu\",\n);\nexport const DropdownMenuCheckboxItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuCheckboxItem() from the server but DropdownMenuCheckboxItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuCheckboxItem\",\n);\nexport const DropdownMenuContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuContent() from the server but DropdownMenuContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuContent\",\n);\nexport const DropdownMenuGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuGroup() from the server but DropdownMenuGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuGroup\",\n);\nexport const DropdownMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuItem() from the server but DropdownMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuItem\",\n);\nexport const DropdownMenuLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuLabel() from the server but DropdownMenuLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuLabel\",\n);\nexport const DropdownMenuPortal = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuPortal() from the server but DropdownMenuPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuPortal\",\n);\nexport const DropdownMenuRadioGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuRadioGroup() from the server but DropdownMenuRadioGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuRadioGroup\",\n);\nexport const DropdownMenuRadioItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuRadioItem() from the server but DropdownMenuRadioItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuRadioItem\",\n);\nexport const DropdownMenuSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuSeparator() from the server but DropdownMenuSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuSeparator\",\n);\nexport const DropdownMenuShortcut = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuShortcut() from the server but DropdownMenuShortcut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuShortcut\",\n);\nexport const DropdownMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuSub() from the server but DropdownMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuSub\",\n);\nexport const DropdownMenuSubContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuSubContent() from the server but DropdownMenuSubContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuSubContent\",\n);\nexport const DropdownMenuSubTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuSubTrigger() from the server but DropdownMenuSubTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuSubTrigger\",\n);\nexport const DropdownMenuTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call DropdownMenuTrigger() from the server but DropdownMenuTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/dropdown-menu.tsx\",\n    \"DropdownMenuTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iDACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,iDACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,iDACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,iDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,iDACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,iDACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,iDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iDACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,iDACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,iDACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,iDACA", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/core/PrimaryNavbar.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\nimport {\r\n  NavigationMenu,\r\n  NavigationMenuList,\r\n  NavigationMenuItem,\r\n  NavigationMenuContent,\r\n  NavigationMenuTrigger,\r\n} from \"@/components/ui/navigation-menu\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { ShoppingCart } from \"lucide-react\"\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\"\r\nimport { Sun, Moon } from \"lucide-react\"\r\nimport { useTheme } from 'next-themes'\r\n\r\nconst PrimaryNavbar = () => {\r\n  const { setTheme } = useTheme()\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-900 shadow\">\r\n      <div className=\"container mx-auto px-4 py-3 flex items-center justify-between md:flex-row flex-col\">\r\n        {/* Left Section */}\r\n        <div className=\"flex items-center space-x-4 md:flex-row flex-col\">\r\n          <Link href=\"/app/feed\" className=\"font-bold text-lg\">\r\n            Quikrsolutions.app\r\n          </Link>\r\n          <Link href=\"/app/feed\">Home</Link>\r\n          <NavigationMenu>\r\n            <NavigationMenuList>\r\n              <NavigationMenuItem>\r\n                <NavigationMenuTrigger>Features</NavigationMenuTrigger>\r\n                <NavigationMenuContent>\r\n                  <ul>\r\n                    <li><Link href=\"/app/places\">Places Directory</Link></li>\r\n                    <li><Link href=\"/app/marketplace\">Marketplace Hub</Link></li>\r\n                    <li><Link href=\"/app/foodcourt\">FoodCourt</Link></li>\r\n                    <li><Link href=\"/app/products\">Product Catalogue</Link></li>\r\n                    <li><Link href=\"/app/services\">Service Providers</Link></li>\r\n                    <li><Link href=\"/app/rentals\">Rentals Hub</Link></li>\r\n                  </ul>\r\n                </NavigationMenuContent>\r\n              </NavigationMenuItem>\r\n            </NavigationMenuList>\r\n          </NavigationMenu>\r\n          <Link href=\"/app/pricing\">Pricing</Link>\r\n          <NavigationMenu>\r\n            <NavigationMenuList>\r\n              <NavigationMenuItem>\r\n                <NavigationMenuTrigger>More</NavigationMenuTrigger>\r\n                <NavigationMenuContent>\r\n                  <ul>\r\n                    <li><Link href=\"/app/jobs\">Job Listings</Link></li>\r\n                    <li><Link href=\"/app/graphic-trends\">Graphic Trends</Link></li>\r\n                    <li><Link href=\"/app/trends-advice\">Business Trends & AI Advice</Link></li>\r\n                    <li><Link href=\"/app/ai-arbitrage\">AI Arbitrage Engine</Link></li>\r\n                    <li><Link href=\"/app/ai-tools\">AI Agentic Tools</Link></li>\r\n                    <li><Link href=\"/app/portfolio\">Portfolio</Link></li>\r\n                    <li><Link href=\"/app/contact\">Contact Us</Link></li>\r\n                  </ul>\r\n                </NavigationMenuContent>\r\n              </NavigationMenuItem>\r\n            </NavigationMenuList>\r\n          </NavigationMenu>\r\n          <Link href=\"/app/about\">About</Link>\r\n        </div>\r\n\r\n        {/* Right Section */}\r\n        <div className=\"flex items-center space-x-4 md:flex-row flex-col\">\r\n          <Input\r\n            type=\"text\"\r\n            placeholder=\"Search...\"\r\n            className=\"max-w-xs\"\r\n            onSubmit={() => console.log('Search submitted')}\r\n          />\r\n          <Button variant=\"ghost\">\r\n            <ShoppingCart className=\"h-4 w-4\" />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            onClick={() => setTheme(theme => theme === 'dark' ? 'light' : 'dark')}\r\n          >\r\n            <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n            <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n            <span className=\"sr-only\">Toggle theme</span>\r\n          </Button>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                <Avatar className=\"h-8 w-8\">\r\n                  <AvatarImage src=\"https://github.com/shadcn.png\" alt=\"@shadcn\" />\r\n                  <AvatarFallback>CN</AvatarFallback>\r\n                </Avatar>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\r\n              <DropdownMenuItem>\r\n                Dashboard\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem>\r\n                My Profile\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem>\r\n                Settings\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem>\r\n                Invoices/Receipts\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem>\r\n                Logout\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PrimaryNavbar;"], "names": [], "mappings": ";;;;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AAOA;AAAA;AACA;;;;;;;;;;;AAEA,MAAM,gBAAgB;IACpB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAU;sCAAoB;;;;;;sCAGrD,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAY;;;;;;sCACvB,8OAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;0CACjB,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;;sDACjB,8OAAC,8IAAA,CAAA,wBAAqB;sDAAC;;;;;;sDACvB,8OAAC,8IAAA,CAAA,wBAAqB;sDACpB,cAAA,8OAAC;;kEACC,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAc;;;;;;;;;;;kEAC7B,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAmB;;;;;;;;;;;kEAClC,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAiB;;;;;;;;;;;kEAChC,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAgB;;;;;;;;;;;kEAC/B,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAgB;;;;;;;;;;;kEAC/B,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMxC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAe;;;;;;sCAC1B,8OAAC,8IAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;0CACjB,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;;sDACjB,8OAAC,8IAAA,CAAA,wBAAqB;sDAAC;;;;;;sDACvB,8OAAC,8IAAA,CAAA,wBAAqB;sDACpB,cAAA,8OAAC;;kEACC,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAY;;;;;;;;;;;kEAC3B,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAsB;;;;;;;;;;;kEACrC,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAqB;;;;;;;;;;;kEACpC,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAoB;;;;;;;;;;;kEACnC,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAgB;;;;;;;;;;;kEAC/B,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAiB;;;;;;;;;;;kEAChC,8OAAC;kEAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMxC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAa;;;;;;;;;;;;8BAI1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,UAAU,IAAM,QAAQ,GAAG,CAAC;;;;;;sCAE9B,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;sCACd,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;sCAE1B,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,SAAS,CAAA,QAAS,UAAU,SAAS,UAAU;;8CAE9D,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAE5B,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAI;oDAAgC,KAAI;;;;;;8DACrD,8OAAC,kIAAA,CAAA,iBAAc;8DAAC;;;;;;;;;;;;;;;;;;;;;;8CAItB,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;sDAC1D,8OAAC,4IAAA,CAAA,mBAAgB;sDAAC;;;;;;sDAGlB,8OAAC,4IAAA,CAAA,mBAAgB;sDAAC;;;;;;sDAGlB,8OAAC,4IAAA,CAAA,mBAAgB;sDAAC;;;;;;sDAGlB,8OAAC,4IAAA,CAAA,mBAAgB;sDAAC;;;;;;sDAGlB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShC;uCAEe", "debugId": null}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/popover.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Popover = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx <module evaluation>\",\n    \"Popover\",\n);\nexport const PopoverContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverContent() from the server but PopoverContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx <module evaluation>\",\n    \"PopoverContent\",\n);\nexport const PopoverTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverTrigger() from the server but PopoverTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx <module evaluation>\",\n    \"PopoverTrigger\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA", "debugId": null}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/popover.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Popover = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx\",\n    \"Popover\",\n);\nexport const PopoverContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverContent() from the server but PopoverContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx\",\n    \"PopoverContent\",\n);\nexport const PopoverTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverTrigger() from the server but PopoverTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx\",\n    \"PopoverTrigger\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA", "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/core/SecondaryNavbar.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { <PERSON><PERSON><PERSON>, <PERSON> } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\n\r\nconst SecondaryNavbar = () => {\r\n  return (\r\n    <div className=\"w-full h-10 bg-gray-100 border-b border-gray-200 flex items-center justify-between px-4\">\r\n      {/* Weather Display */}\r\n      <div className=\"flex items-center\">\r\n        <CloudSun className=\"mr-2 h-4 w-4\" />\r\n        <span>25°C, Sunny</span>\r\n      </div>\r\n\r\n      {/* Marquee Component */}\r\n      <div className=\"marquee\">\r\n        <span className=\"marquee-text\">\r\n          Platform Update: Enhanced AI tools available in 'More' section! | Welcome to Quikrsolutions - Your All-in-One Hub! | Special: Get 10% off your first FoodCourt order this week!\r\n        </span>\r\n      </div>\r\n\r\n      {/* Platform Notifications Icon & Popover */}\r\n      <div>\r\n        <Popover>\r\n          <PopoverTrigger asChild>\r\n            <Button variant=\"ghost\" size=\"icon\">\r\n              <Bell className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Notifications</span>\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-80 p-4\">\r\n            <div className=\"grid gap-4\">\r\n              <p className=\"text-sm font-medium\">Notifications</p>\r\n              <div className=\"grid gap-2\">\r\n                <div className=\"border-b border-gray-200 pb-2\">\r\n                  <p className=\"text-sm\">New feature: Remittances now live!</p>\r\n                </div>\r\n                <div className=\"border-b border-gray-200 pb-2\">\r\n                  <p className=\"text-sm\">Scheduled maintenance on Sunday at 2 AM UTC.</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm\">Special: Get 10% off your first FoodCourt order this week!</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SecondaryNavbar;"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;;;;;AAMA,MAAM,kBAAkB;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;kCAAK;;;;;;;;;;;;0BAIR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAAe;;;;;;;;;;;0BAMjC,8OAAC;0BACC,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sCACN,8OAAC,mIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;;kDAC3B,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAG9B,8OAAC,mIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACxB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;0DAEzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;0DAEzB,8OAAC;0DACC,cAAA,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;uCAEe", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/app/layout.tsx"], "sourcesContent": ["// src/app/layout.tsx\nimport { GeistSans } from 'geist/font/sans'; // Or Inter, or your chosen font\n// import { GeistMono } from 'geist/font/mono'; // If using\nimport './globals.css';\nimport { AuthProvider } from '@/contexts/authcontext'; // Import AuthProvider\nimport { Toaster } from '@/components/ui/sonner'; // Import Sonner Toaster\nimport PrimaryNavbar from '@/components/core/PrimaryNavbar';\nimport SecondaryNavbar from '@/components/core/SecondaryNavbar';\nimport { createSupabaseServerClient } from '@/lib/supabase/server';\nimport { ThemeProvider } from 'next-themes';\n\nexport const metadata = {\n  title: 'Quikrsolutions.app',\n  description: 'Your entire business, intelligently connected.',\n};\n\nexport default async function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang=\"en\" className={`${GeistSans.variable}`}>\n      <head>\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n      </head>\n      <body>\n        <ThemeProvider attribute=\"class\" defaultTheme=\"system\" enableSystem>\n          <AuthProvider>\n            <PrimaryNavbar />\n            <SecondaryNavbar />\n            <main className=\"container mx-auto py-10\">{children}</main>\n            <Toaster richColors position=\"top-right\" /> {/* Add Sonner Toaster here */}\n          </AuthProvider>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;;AACrB,yRAA6C,gCAAgC;AAA7E;AAGA,gOAAuD,sBAAsB;AAC7E,mOAAkD,wBAAwB;AAC1E;AACA;AAEA;;;;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,eAAe,WAAW,EACvC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,GAAG,2LAAA,CAAA,YAAS,CAAC,QAAQ,EAAE;;0BAChD,8OAAC;0BACC,cAAA,8OAAC;oBAAK,KAAI;oBAAO,MAAK;;;;;;;;;;;0BAExB,8OAAC;0BACC,cAAA,8OAAC,gJAAA,CAAA,gBAAa;oBAAC,WAAU;oBAAQ,cAAa;oBAAS,YAAY;8BACjE,cAAA,8OAAC,+HAAA,CAAA,eAAY;;0CACX,8OAAC,2IAAA,CAAA,UAAa;;;;;0CACd,8OAAC,6IAAA,CAAA,UAAe;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAA2B;;;;;;0CAC3C,8OAAC,kIAAA,CAAA,UAAO;gCAAC,UAAU;gCAAC,UAAS;;;;;;4BAAc;;;;;;;;;;;;;;;;;;;;;;;AAMvD", "debugId": null}}]}