// src/app/layout.tsx
import { GeistSans } from 'geist/font/sans'; // Or Inter, or your chosen font
// import { GeistMono } from 'geist/font/mono'; // If using
import './globals.css';
import { AuthProvider } from '@/contexts/authcontext'; // Import AuthProvider
import { Toaster } from '@/components/ui/sonner'; // Import Sonner Toaster
import PrimaryNavbar from '@/components/core/PrimaryNavbar';
import SecondaryNavbar from '@/components/core/SecondaryNavbar';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { ThemeProvider } from 'next-themes';

export const metadata = {
  title: 'Quikrsolutions.app',
  description: 'Your entire business, intelligently connected.',
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${GeistSans.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <AuthProvider>
            <PrimaryNavbar />
            <SecondaryNavbar />
            <main className="container mx-auto py-10">{children}</main>
            <Toaster richColors position="top-right" /> {/* Add Sonner Toaster here */}
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
