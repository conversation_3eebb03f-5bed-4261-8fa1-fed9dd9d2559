"use client"

import React from 'react';
import Link from 'next/link';
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuContent,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { ShoppingCart } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Sun, Moon } from "lucide-react"
import { useTheme } from 'next-themes'

const PrimaryNavbar = () => {
  const { setTheme } = useTheme()

  return (
    <div className="bg-white dark:bg-gray-900 shadow">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between md:flex-row flex-col">
        {/* Left Section */}
        <div className="flex items-center space-x-4 md:flex-row flex-col">
          <Link href="/app/feed" className="font-bold text-lg">
            Quikrsolutions.app
          </Link>
          <Link href="/app/feed">Home</Link>
          <NavigationMenu>
            <NavigationMenuList>
              <NavigationMenuItem>
                <NavigationMenuTrigger>Features</NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul>
                    <li><Link href="/app/places">Places Directory</Link></li>
                    <li><Link href="/app/marketplace">Marketplace Hub</Link></li>
                    <li><Link href="/app/foodcourt">FoodCourt</Link></li>
                    <li><Link href="/app/products">Product Catalogue</Link></li>
                    <li><Link href="/app/services">Service Providers</Link></li>
                    <li><Link href="/app/rentals">Rentals Hub</Link></li>
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
          <Link href="/app/pricing">Pricing</Link>
          <NavigationMenu>
            <NavigationMenuList>
              <NavigationMenuItem>
                <NavigationMenuTrigger>More</NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul>
                    <li><Link href="/app/jobs">Job Listings</Link></li>
                    <li><Link href="/app/graphic-trends">Graphic Trends</Link></li>
                    <li><Link href="/app/trends-advice">Business Trends & AI Advice</Link></li>
                    <li><Link href="/app/ai-arbitrage">AI Arbitrage Engine</Link></li>
                    <li><Link href="/app/ai-tools">AI Agentic Tools</Link></li>
                    <li><Link href="/app/portfolio">Portfolio</Link></li>
                    <li><Link href="/app/contact">Contact Us</Link></li>
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
          <Link href="/app/about">About</Link>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-4 md:flex-row flex-col">
          <Input
            type="text"
            placeholder="Search..."
            className="max-w-xs"
            onSubmit={() => console.log('Search submitted')}
          />
          <Button variant="ghost">
            <ShoppingCart className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setTheme(theme => theme === 'dark' ? 'light' : 'dark')}
          >
            <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
                  <AvatarFallback>CN</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuItem>
                Dashboard
              </DropdownMenuItem>
              <DropdownMenuItem>
                My Profile
              </DropdownMenuItem>
              <DropdownMenuItem>
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem>
                Invoices/Receipts
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

export default PrimaryNavbar;