'use client';

import * as React from 'react';
import { User } from '@supabase/supabase-js';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { updateUserRole } from '@/app/admin/actions';
import { toast } from 'sonner';

interface UserRoleSelectProps {
  user: User;
  availableRoles: string[];
}

export function UserRoleSelect({ user, availableRoles }: UserRoleSelectProps) {
  // Determine the initial role to display
  const initialRole = user.app_metadata?.is_admin === true ? 'admin' : user.app_metadata?.role || user.role;

  const [selectedRole, setSelectedRole] = React.useState(initialRole);
  const [isUpdating, setIsUpdating] = React.useState(false);

  const handleRoleChange = async (newRole: string) => {
    if (newRole === selectedRole) return; // No change

    setIsUpdating(true);
    const result = await updateUserRole(user.id, newRole === 'authenticated' ? null : newRole); // Pass null for default role

    if (result.success) {
      setSelectedRole(newRole);
      toast.success(result.message);
    } else {
      toast.error(result.message);
      // Revert the select value on error
      setSelectedRole(initialRole);
    }
    setIsUpdating(false);
  };

  return (
    <Select onValueChange={handleRoleChange} value={selectedRole} disabled={isUpdating}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Select Role" />
      </SelectTrigger>
      <SelectContent>
        {/* Always include the default authenticated role */}
        <SelectItem value="authenticated">authenticated</SelectItem>
        {/* Include 'admin' if it's in availableRoles or if the user is currently an admin */}
        {(availableRoles.includes('admin') || initialRole === 'admin') && (
           <SelectItem value="admin">Admin</SelectItem>
        )}
        {/* Include other available roles */}
        {availableRoles
          .filter(role => role !== 'admin' && role !== 'authenticated') // Exclude admin and authenticated from this list
          .map(role => (
          <SelectItem key={role} value={role}>{role}</SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
