declare module '@nowpaymentsio/nowpayments-api-js' {
  export interface CreatePaymentReturn {
    payment_id: number;
    payment_status: string;
    pay_address: string;
    price_amount: number;
    price_currency: string;
    pay_amount: number;
    pay_currency: string;
    order_id: string;
    order_description: string;
    purchase_id: number;
    created_at: string;
    updated_at: string;
    outcome_amount: number;
    outcome_currency: string;
  }

  export interface GetPaymentStatusReturn {
    payment_id: number;
    payment_status: string;
    pay_address: string;
    price_amount: number;
    price_currency: string;
    pay_amount: number;
    actually_paid: number;
    pay_currency: string;
    order_id: string;
    order_description: string;
    purchase_id: number;
    created_at: string;
    updated_at: string;
    outcome_amount: number;
    outcome_currency: string;
  }

  export interface CurrenciesResponse {
    currencies: string[];
  }

  export interface Options {
    apiKey: string;
  }

  class NOWPaymentsApi {
    constructor(options: Options);
    createPayment(params: any): Promise<CreatePaymentReturn>;
    getPaymentStatus(params: { payment_id: string }): Promise<GetPaymentStatusReturn>;
    getCurrencies(): Promise<CurrenciesResponse>;
  }

  export default NOWPaymentsApi;
}
