import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, Star, MapPin, Truck } from "lucide-react"

export default function FoodCourtPage() {
  const restaurants = [
    {
      id: 1,
      name: "Mario's Italian Kitchen",
      cuisine: "Italian",
      rating: 4.8,
      reviews: 234,
      deliveryTime: "25-35 min",
      deliveryFee: 2.99,
      minOrder: 15,
      image: "/api/placeholder/300/200",
      featured: ["Pizza", "Pasta", "Risotto"]
    },
    {
      id: 2,
      name: "Dragon Palace",
      cuisine: "Chinese",
      rating: 4.6,
      reviews: 189,
      deliveryTime: "30-40 min",
      deliveryFee: 3.49,
      minOrder: 20,
      image: "/api/placeholder/300/200",
      featured: ["Dim Sum", "Noodles", "Fried Rice"]
    },
    {
      id: 3,
      name: "Burger Junction",
      cuisine: "American",
      rating: 4.7,
      reviews: 156,
      deliveryTime: "20-30 min",
      deliveryFee: 1.99,
      minOrder: 12,
      image: "/api/placeholder/300/200",
      featured: ["Burgers", "Fries", "Milkshakes"]
    },
    {
      id: 4,
      name: "Spice Garden",
      cuisine: "Indian",
      rating: 4.9,
      reviews: 298,
      deliveryTime: "35-45 min",
      deliveryFee: 2.49,
      minOrder: 18,
      image: "/api/placeholder/300/200",
      featured: ["Curry", "Biryani", "Naan"]
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">FoodCourt</h1>
          <p className="text-muted-foreground">Order delicious food from local restaurants</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">View Cart (0)</Button>
          <Button>Track Order</Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <Input 
          placeholder="Search restaurants or dishes..." 
          className="max-w-md"
        />
        <Button variant="outline">Cuisine Type</Button>
        <Button variant="outline">Delivery Time</Button>
        <Button variant="outline">Rating</Button>
      </div>

      {/* Quick Filters */}
      <div className="flex gap-2 flex-wrap">
        <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
          🍕 Pizza
        </Badge>
        <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
          🍔 Burgers
        </Badge>
        <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
          🍜 Asian
        </Badge>
        <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
          🥗 Healthy
        </Badge>
        <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
          🍰 Desserts
        </Badge>
      </div>

      {/* Special Offer Banner */}
      <Card className="bg-gradient-to-r from-orange-500 to-red-500 text-white">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold">Special Offer!</h3>
              <p className="text-orange-100">Get 10% off your first order this week</p>
            </div>
            <Button variant="secondary">Order Now</Button>
          </div>
        </CardContent>
      </Card>

      {/* Restaurants Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {restaurants.map((restaurant) => (
          <Card key={restaurant.id} className="hover:shadow-lg transition-shadow cursor-pointer">
            <div className="aspect-video bg-muted rounded-t-lg flex items-center justify-center">
              <span className="text-muted-foreground">Restaurant Image</span>
            </div>
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{restaurant.name}</CardTitle>
                  <Badge variant="secondary" className="mt-1">{restaurant.cuisine}</Badge>
                </div>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{restaurant.rating}</span>
                  <span className="text-xs text-muted-foreground">({restaurant.reviews})</span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex flex-wrap gap-1">
                {restaurant.featured.map((item, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {item}
                  </Badge>
                ))}
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{restaurant.deliveryTime}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Truck className="h-4 w-4 text-muted-foreground" />
                  <span>${restaurant.deliveryFee}</span>
                </div>
              </div>
              
              <div className="text-sm text-muted-foreground">
                Min order: ${restaurant.minOrder}
              </div>
              
              <Button className="w-full">View Menu</Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      <div className="text-center">
        <Button variant="outline">Load More Restaurants</Button>
      </div>
    </div>
  )
}
