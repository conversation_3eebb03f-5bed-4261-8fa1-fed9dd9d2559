'use client';

import React, { useState, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import { createStripePortalSession } from '@/app/actions/stripeActions';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react'; // For loading spinner

interface ManageSubscriptionButtonProps {
  // No props needed if it's always rendered when a subscription exists
  // and it handles its own logic.
  // We could pass a className or variant if needed for styling.
  className?: string;
}

export function ManageSubscriptionButton({ className }: ManageSubscriptionButtonProps) {
  const [isPending, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(false);

  const handleManageSubscription = async () => {
    setIsLoading(true);
    startTransition(async () => {
      const result = await createStripePortalSession();
      if (result.error) {
        toast.error(result.error);
        setIsLoading(false);
      } else if (result.url) {
        // No need to set isLoading to false here as we are redirecting
        window.location.href = result.url;
      } else {
        // Fallback, should not happen if action is well-defined
        toast.error('Could not open the billing portal. Please try again.');
        setIsLoading(false);
      }
    });
  };

  return (
    <Button
      variant="outline"
      className={`w-full mt-4 ${className}`}
      onClick={handleManageSubscription}
      disabled={isLoading || isPending}
    >
      {isLoading || isPending ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : null}
      Manage Subscription
    </Button>
  );
}