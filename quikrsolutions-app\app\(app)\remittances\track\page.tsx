'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useRealtimeRemittance } from '@/hooks/useRealtimeRemittance';

// Define the type for the remittance details fetched initially and updated by realtime
interface RemittanceDetails {
  id: string;
  status: string; // e.g., 'pending', 'processing', 'completed', 'failed'
  // Add other relevant tracking details here that you fetch initially
  // For example:
  // amount: number;
  // currency: string;
  // recipientName: string;
  // createdAt: string;
}


export default function RemittanceTrackingPage() {
  const params = useParams();
  const remittanceId = params.id as string; // Assuming the ID is passed as a URL parameter like /remittances/track/[id]

  // Use the hook to get realtime updates and initial data (if hook fetches it)
  // For now, the hook only provides realtime updates, so we'll fetch initial data separately.
  const { remittanceDetails: realtimeRemittanceDetails, loading: realtimeLoading, error: realtimeError } = useRealtimeRemittance(remittanceId);

  const [initialRemittanceDetails, setInitialRemittanceDetails] = useState<RemittanceDetails | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [initialError, setInitialError] = useState<string | null>(null);


  // TODO: Fetch initial remittance status based on remittanceId
  useEffect(() => {
    if (!remittanceId) {
      setInitialError('Remittance ID is missing.');
      setInitialLoading(false);
      return;
    }

    const fetchStatus = async () => {
      try {
        // Replace with actual API call to fetch status by ID
        // Example: const response = await fetch(`/api/quikr/remittances/${remittanceId}/details`); // New API route for details
        // const data = await response.json();
        // if (response.ok) {
        //   setInitialRemittanceDetails(data.data); // Assuming API returns { status: 'success', data: {...} }
        // } else {
        //   setInitialError(data.message || 'Failed to fetch remittance details.');
        // }


        // Mock data for now
        const mockDetails: RemittanceDetails = {
          id: remittanceId,
          status: 'initial fetch...', // Placeholder status
           // Add other mock details here
        };
        setInitialRemittanceDetails(mockDetails);


      } catch (err: any) {
        setInitialError('Failed to fetch remittance details.');
        console.error('Error fetching details:', err);
      } finally {
        setInitialLoading(false);
      }
    };

    fetchStatus();
  }, [remittanceId]);


  // Determine the current remittance details to display: realtime updates take precedence
  const currentRemittanceDetails = realtimeRemittanceDetails || initialRemittanceDetails;
  const isLoading = initialLoading || realtimeLoading;
  const displayError = initialError || realtimeError;


  if (isLoading) {
    return <div className="container mx-auto py-8">Loading status for Remittance ID: {remittanceId}...</div>;
  }

  if (displayError) {
    return <div className="container mx-auto py-8 text-red-500">{displayError}</div>;
  }

  if (!currentRemittanceDetails) {
    return <div className="container mx-auto py-8">Remittance with ID {remittanceId} not found.</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Track Remittance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p><strong>Remittance ID:</strong> {currentRemittanceDetails.id}</p>
            <p><strong>Current Status:</strong> {currentRemittanceDetails.status}</p>
            {/* Display other remittance details here */}
            {/* Example: */}
            {/* <p><strong>Amount:</strong> {currentRemittanceDetails.amount} {currentRemittanceDetails.currency}</p> */}
            {/* <p><strong>Recipient:</strong> {currentRemittanceDetails.recipientName}</p> */}
            {/* <p><strong>Initiated At:</strong> {new Date(currentRemittanceDetails.createdAt).toLocaleString()}</p> */}

            {/* TODO: Add Status History/Timeline display */}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
