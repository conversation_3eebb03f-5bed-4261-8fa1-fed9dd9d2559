import React from 'react';
import { redirect } from 'next/navigation';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { STRIPE_PRICE_IDS } from '@/lib/constants';
import SubscriptionClientContent from './SubscriptionClientContent';

// Define plans using imported Price IDs
const plans = [
  { name: "Basic Plan", price: "$10/month", priceId: STRIPE_PRICE_IDS.BASIC_MONTHLY, description: "Access to basic features." },
  { name: "Premium Plan", price: "$20/month", priceId: STRIPE_PRICE_IDS.PREMIUM_MONTHLY, description: "Access to all premium features." },
];

export default async function SubscriptionsPage() {
  const supabase = await createSupabaseServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login'); // Protect the route
  }

  // Fetch user profile to pass to client component
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  if (profileError || !profile) {
    console.error('Error fetching user profile:', profileError);
    // Handle error, maybe redirect to an error page or show a message
    return <div>Error loading profile.</div>;
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8 text-center">Choose Your Plan</h1>
      <SubscriptionClientContent user={profile} plans={plans} />
    </div>
  );
}
