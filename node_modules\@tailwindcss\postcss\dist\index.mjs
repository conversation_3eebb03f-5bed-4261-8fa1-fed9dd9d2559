var $=(l,i)=>(i=Symbol[l])?i:Symbol.for("Symbol."+l),L=l=>{throw TypeError(l)};var B=(l,i,n)=>{if(i!=null){typeof i!="object"&&typeof i!="function"&&L("Object expected");var e,s;n&&(e=i[$("asyncDispose")]),e===void 0&&(e=i[$("dispose")],n&&(s=e)),typeof e!="function"&&L("Object not disposable"),s&&(e=function(){try{s.call(this)}catch(t){return Promise.reject(t)}}),l.push([n,e,i])}else n&&l.push([n]);return i},U=(l,i,n)=>{var e=typeof SuppressedError=="function"?SuppressedError:function(r,o,u,p){return p=Error(u),p.name="SuppressedError",p.error=r,p.suppressed=o,p},s=r=>i=n?new e(r,i,"An error was suppressed during disposal"):(n=!0,r),t=r=>{for(;r=l.pop();)try{var o=r[1]&&r[1].call(r[2]);if(r[0])return Promise.resolve(o).then(t,u=>(s(u),t()))}catch(u){s(u)}if(n)throw i};return t()};import J from"@alloc/quick-lru";import{compileAst as V,env as Z,Features as E,Instrumentation as ee}from"@tailwindcss/node";import{clearRequireCache as te}from"@tailwindcss/node/require-cache";import{Scanner as se}from"@tailwindcss/oxide";import{Features as y,transform as ie}from"lightningcss";import re from"node:fs";import m,{relative as W}from"node:path";import v from"postcss";var F=32;var H=40;function _(l,i=[]){for(let n=5;n<l.length;n++){let e=l.charCodeAt(n);if(e===F||e===H){let s=l.slice(0,n).trim(),t=l.slice(n).trim();return g(s,t,i)}}return g(l.trim(),"",i)}var q=64;function X(l,i=[]){return{kind:"rule",selector:l,nodes:i}}function g(l,i="",n=[]){return{kind:"at-rule",name:l,params:i,nodes:n}}function k(l,i=[]){return l.charCodeAt(0)===q?_(l,i):X(l,i)}function N(l,i,n=!1){return{kind:"declaration",property:l,value:i,important:n}}function P(l){return{kind:"comment",value:l}}function D(l){function i(e,s=0){let t="",r="  ".repeat(s);if(e.kind==="declaration")t+=`${r}${e.property}: ${e.value}${e.important?" !important":""};
`;else if(e.kind==="rule"){t+=`${r}${e.selector} {
`;for(let o of e.nodes)t+=i(o,s+1);t+=`${r}}
`}else if(e.kind==="at-rule"){if(e.nodes.length===0)return`${r}${e.name} ${e.params};
`;t+=`${r}${e.name}${e.params?` ${e.params} `:" "}{
`;for(let o of e.nodes)t+=i(o,s+1);t+=`${r}}
`}else if(e.kind==="comment")t+=`${r}/*${e.value}*/
`;else if(e.kind==="context"||e.kind==="at-root")return"";return t}let n="";for(let e of l){let s=i(e);s!==""&&(n+=s)}return n}import h from"postcss";var Y=33;function z(l,i){let n=h.root();n.source=i;function e(s,t){if(s.kind==="declaration"){let r=h.decl({prop:s.property,value:s.value??"",important:s.important});r.source=i,t.append(r)}else if(s.kind==="rule"){let r=h.rule({selector:s.selector});r.source=i,r.raws.semicolon=!0,t.append(r);for(let o of s.nodes)e(o,r)}else if(s.kind==="at-rule"){let r=h.atRule({name:s.name.slice(1),params:s.params});r.source=i,r.raws.semicolon=!0,t.append(r);for(let o of s.nodes)e(o,r)}else if(s.kind==="comment"){let r=h.comment({text:s.value});r.raws.left="",r.raws.right="",r.source=i,t.append(r)}else s.kind==="at-root"||s.kind}for(let s of l)e(s,n);return n}function M(l){function i(e,s){if(e.type==="decl")s.push(N(e.prop,e.value,e.important));else if(e.type==="rule"){let t=k(e.selector);e.each(r=>i(r,t.nodes)),s.push(t)}else if(e.type==="atrule"){let t=g(`@${e.name}`,e.params);e.each(r=>i(r,t.nodes)),s.push(t)}else if(e.type==="comment"){if(e.text.charCodeAt(0)!==Y)return;s.push(P(e.text))}}let n=[];return l.each(e=>i(e,n)),n}import{normalizePath as I}from"@tailwindcss/node";import x from"node:path";var R="'",w='"';function b(){let l=new WeakSet;function i(n){let e=n.root().source?.input.file;if(!e)return;let s=n.source?.input.file;if(!s||l.has(n))return;let t=n.params[0],r=t[0]===w&&t[t.length-1]===w?w:t[0]===R&&t[t.length-1]===R?R:null;if(!r)return;let o=n.params.slice(1,-1),u="";if(o.startsWith("!")&&(o=o.slice(1),u="!"),!o.startsWith("./")&&!o.startsWith("../"))return;let p=x.posix.join(I(x.dirname(s)),o),C=x.posix.dirname(I(e)),d=x.posix.relative(C,p);d.startsWith(".")||(d="./"+d),n.params=r+u+d+r,l.add(n)}return{postcssPlugin:"tailwindcss-postcss-fix-relative-paths",Once(n){n.walkAtRules(/source|plugin|config/,i)}}}var a=Z.DEBUG,T=new J({maxSize:50});function ne(l,i){let n=`${l}:${i.base??""}:${JSON.stringify(i.optimize)}`;if(T.has(n))return T.get(n);let e={mtimes:new Map,compiler:null,scanner:null,tailwindCssAst:[],cachedPostCssAst:v.root(),optimizedPostCssAst:v.root(),fullRebuildPaths:[]};return T.set(n,e),e}function oe(l={}){let i=l.base??process.cwd(),n=l.optimize??process.env.NODE_ENV==="production";return{postcssPlugin:"@tailwindcss/postcss",plugins:[b(),{postcssPlugin:"tailwindcss",async Once(e,{result:s}){var O=[];try{let t=B(O,new ee);let r=s.opts.from??"";a&&t.start(`[@tailwindcss/postcss] ${W(i,r)}`);{a&&t.start("Quick bail check");let f=!0;if(e.walkAtRules(c=>{if(c.name==="import"||c.name==="theme"||c.name==="config"||c.name==="plugin"||c.name==="apply")return f=!1,!1}),f)return;a&&t.end("Quick bail check")}let o=ne(r,l);let u=m.dirname(m.resolve(r));async function p(){a&&t.start("Setup compiler"),o.fullRebuildPaths.length>0&&!C&&te(o.fullRebuildPaths),o.fullRebuildPaths=[],a&&t.start("PostCSS AST -> Tailwind CSS AST");let f=M(e);a&&t.end("PostCSS AST -> Tailwind CSS AST"),a&&t.start("Create compiler");let c=await V(f,{base:u,onDependency:A=>{o.fullRebuildPaths.push(A)}});return a&&t.end("Create compiler"),a&&t.end("Setup compiler"),c}let C=o.compiler===null;o.compiler??=await p();if(o.compiler.features===E.None)return;let d="incremental";a&&t.start("Register full rebuild paths");{for(let c of o.fullRebuildPaths)s.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:m.resolve(c),parent:s.opts.from});let f=s.messages.flatMap(c=>c.type!=="dependency"?[]:c.file);f.push(r);for(let c of f){let A=re.statSync(c,{throwIfNoEntry:!1})?.mtimeMs??null;if(A===null){c===r&&(d="full");continue}o.mtimes.get(c)!==A&&(d="full",o.mtimes.set(c,A))}}a&&t.end("Register full rebuild paths");d==="full"&&!C&&(o.compiler=await p());if(o.scanner===null||d==="full"){a&&t.start("Setup scanner");let f=(o.compiler.root==="none"?[]:o.compiler.root===null?[{base:i,pattern:"**/*"}]:[o.compiler.root]).concat(o.compiler.globs);o.scanner=new se({sources:f}),a&&t.end("Setup scanner")}a&&t.start("Scan for candidates");let G=o.compiler.features&E.Utilities?o.scanner.scan():[];a&&t.end("Scan for candidates");if(o.compiler.features&E.Utilities){a&&t.start("Register dependency messages");for(let f of o.scanner.files)s.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:m.resolve(f),parent:s.opts.from});for(let{base:f,pattern:c}of o.scanner.globs)c==="*"&&i===f||(c===""?s.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:m.resolve(f),parent:s.opts.from}):s.messages.push({type:"dir-dependency",plugin:"@tailwindcss/postcss",dir:m.resolve(f),glob:c,parent:s.opts.from}));a&&t.end("Register dependency messages")}a&&t.start("Build utilities");let S=o.compiler.build(G);a&&t.end("Build utilities");if(o.tailwindCssAst!==S)if(n){a&&t.start("Optimization"),a&&t.start("AST -> CSS");let f=D(S);a&&t.end("AST -> CSS"),a&&t.start("Lightning CSS");let c=le(f,{minify:typeof n=="object"?n.minify:!0});a&&t.end("Lightning CSS"),a&&t.start("CSS -> PostCSS AST"),o.optimizedPostCssAst=v.parse(c,s.opts),a&&t.end("CSS -> PostCSS AST"),a&&t.end("Optimization")}else a&&t.start("Transform Tailwind CSS AST into PostCSS AST"),o.cachedPostCssAst=z(S,e.source),a&&t.end("Transform Tailwind CSS AST into PostCSS AST");o.tailwindCssAst=S;a&&t.start("Update PostCSS AST");e.removeAll();e.append(n?o.optimizedPostCssAst.clone().nodes:o.cachedPostCssAst.clone().nodes);e.raws.indent="  ";a&&t.end("Update PostCSS AST");a&&t.end(`[@tailwindcss/postcss] ${W(i,r)}`)}catch(K){var Q=K,j=!0}finally{U(O,Q,j)}}}]}}function le(l,{file:i="input.css",minify:n=!1}={}){function e(s){return ie({filename:i,code:s,minify:n,sourceMap:!1,drafts:{customMedia:!0},nonStandard:{deepSelectorCombinator:!0},include:y.Nesting,exclude:y.LogicalProperties|y.DirSelector|y.LightDark,targets:{safari:16<<16|1024,ios_saf:16<<16|1024,firefox:8388608,chrome:7274496},errorRecovery:!0}).code}return e(e(Buffer.from(l))).toString()}var Oe=Object.assign(oe,{postcss:!0});export{Oe as default};
