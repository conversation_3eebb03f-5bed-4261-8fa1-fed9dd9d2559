// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    interface PaymentMethodConfigurationCreateParams {
      /**
       * Canadian pre-authorized debit payments, check this [page](https://stripe.com/docs/payments/acss-debit) for more details like country availability.
       */
      acss_debit?: PaymentMethodConfigurationCreateParams.AcssDebit;

      /**
       * [Affirm](https://www.affirm.com/) gives your customers a way to split purchases over a series of payments. Depending on the purchase, they can pay with four interest-free payments (Split Pay) or pay over a longer term (Installments), which might include interest. Check this [page](https://stripe.com/docs/payments/affirm) for more details like country availability.
       */
      affirm?: PaymentMethodConfigurationCreateParams.Affirm;

      /**
       * Afterpay gives your customers a way to pay for purchases in installments, check this [page](https://stripe.com/docs/payments/afterpay-clearpay) for more details like country availability. Afterpay is particularly popular among businesses selling fashion, beauty, and sports products.
       */
      afterpay_clearpay?: PaymentMethodConfigurationCreateParams.AfterpayClearpay;

      /**
       * Alipay is a digital wallet in China that has more than a billion active users worldwide. Alipay users can pay on the web or on a mobile device using login credentials or their Alipay app. Alipay has a low dispute rate and reduces fraud by authenticating payments using the customer's login credentials. Check this [page](https://stripe.com/docs/payments/alipay) for more details.
       */
      alipay?: PaymentMethodConfigurationCreateParams.Alipay;

      /**
       * Alma is a Buy Now, Pay Later payment method that offers customers the ability to pay in 2, 3, or 4 installments.
       */
      alma?: PaymentMethodConfigurationCreateParams.Alma;

      /**
       * Amazon Pay is a wallet payment method that lets your customers check out the same way as on Amazon.
       */
      amazon_pay?: PaymentMethodConfigurationCreateParams.AmazonPay;

      /**
       * Stripe users can accept [Apple Pay](https://stripe.com/payments/apple-pay) in iOS applications in iOS 9 and later, and on the web in Safari starting with iOS 10 or macOS Sierra. There are no additional fees to process Apple Pay payments, and the [pricing](https://stripe.com/pricing) is the same as other card transactions. Check this [page](https://stripe.com/docs/apple-pay) for more details.
       */
      apple_pay?: PaymentMethodConfigurationCreateParams.ApplePay;

      /**
       * Apple Pay Later, a payment method for customers to buy now and pay later, gives your customers a way to split purchases into four installments across six weeks.
       */
      apple_pay_later?: PaymentMethodConfigurationCreateParams.ApplePayLater;

      /**
       * Stripe users in Australia can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with an Australian bank account. Check this [page](https://stripe.com/docs/payments/au-becs-debit) for more details.
       */
      au_becs_debit?: PaymentMethodConfigurationCreateParams.AuBecsDebit;

      /**
       * Stripe users in the UK can accept Bacs Direct Debit payments from customers with a UK bank account, check this [page](https://stripe.com/docs/payments/payment-methods/bacs-debit) for more details.
       */
      bacs_debit?: PaymentMethodConfigurationCreateParams.BacsDebit;

      /**
       * Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation. [Customers](https://stripe.com/docs/api/customers) use a Bancontact card or mobile app linked to a Belgian bank account to make online payments that are secure, guaranteed, and confirmed immediately. Check this [page](https://stripe.com/docs/payments/bancontact) for more details.
       */
      bancontact?: PaymentMethodConfigurationCreateParams.Bancontact;

      /**
       * Billie is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method that offers businesses Pay by Invoice where they offer payment terms ranging from 7-120 days. Customers are redirected from your website or app, authorize the payment with Billie, then return to your website or app. You get [immediate notification](https://stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
       */
      billie?: PaymentMethodConfigurationCreateParams.Billie;

      /**
       * BLIK is a [single use](https://stripe.com/docs/payments/payment-methods#usage) payment method that requires customers to authenticate their payments. When customers want to pay online using BLIK, they request a six-digit code from their banking application and enter it into the payment collection form. Check this [page](https://stripe.com/docs/payments/blik) for more details.
       */
      blik?: PaymentMethodConfigurationCreateParams.Blik;

      /**
       * Boleto is an official (regulated by the Central Bank of Brazil) payment method in Brazil. Check this [page](https://stripe.com/docs/payments/boleto) for more details.
       */
      boleto?: PaymentMethodConfigurationCreateParams.Boleto;

      /**
       * Cards are a popular way for consumers and businesses to pay online or in person. Stripe supports global and local card networks.
       */
      card?: PaymentMethodConfigurationCreateParams.Card;

      /**
       * Cartes Bancaires is France's local card network. More than 95% of these cards are co-branded with either Visa or Mastercard, meaning you can process these cards over either Cartes Bancaires or the Visa or Mastercard networks. Check this [page](https://stripe.com/docs/payments/cartes-bancaires) for more details.
       */
      cartes_bancaires?: PaymentMethodConfigurationCreateParams.CartesBancaires;

      /**
       * Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet. Check this [page](https://stripe.com/docs/payments/cash-app-pay) for more details.
       */
      cashapp?: PaymentMethodConfigurationCreateParams.Cashapp;

      /**
       * Uses a customer's [cash balance](https://stripe.com/docs/payments/customer-balance) for the payment. The cash balance can be funded via a bank transfer. Check this [page](https://stripe.com/docs/payments/bank-transfers) for more details.
       */
      customer_balance?: PaymentMethodConfigurationCreateParams.CustomerBalance;

      /**
       * EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials. EPS is supported by all Austrian banks and is accepted by over 80% of Austrian online retailers. Check this [page](https://stripe.com/docs/payments/eps) for more details.
       */
      eps?: PaymentMethodConfigurationCreateParams.Eps;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * Financial Process Exchange (FPX) is a Malaysia-based payment method that allows customers to complete transactions online using their bank credentials. Bank Negara Malaysia (BNM), the Central Bank of Malaysia, and eleven other major Malaysian financial institutions are members of the PayNet Group, which owns and operates FPX. It is one of the most popular online payment methods in Malaysia, with nearly 90 million transactions in 2018 according to BNM. Check this [page](https://stripe.com/docs/payments/fpx) for more details.
       */
      fpx?: PaymentMethodConfigurationCreateParams.Fpx;

      /**
       * giropay is a German payment method based on online banking, introduced in 2006. It allows customers to complete transactions online using their online banking environment, with funds debited from their bank account. Depending on their bank, customers confirm payments on giropay using a second factor of authentication or a PIN. giropay accounts for 10% of online checkouts in Germany. Check this [page](https://stripe.com/docs/payments/giropay) for more details.
       */
      giropay?: PaymentMethodConfigurationCreateParams.Giropay;

      /**
       * Google Pay allows customers to make payments in your app or website using any credit or debit card saved to their Google Account, including those from Google Play, YouTube, Chrome, or an Android device. Use the Google Pay API to request any credit or debit card stored in your customer's Google account. Check this [page](https://stripe.com/docs/google-pay) for more details.
       */
      google_pay?: PaymentMethodConfigurationCreateParams.GooglePay;

      /**
       * GrabPay is a payment method developed by [Grab](https://www.grab.com/sg/consumer/finance/pay/). GrabPay is a digital wallet - customers maintain a balance in their wallets that they pay out with. Check this [page](https://stripe.com/docs/payments/grabpay) for more details.
       */
      grabpay?: PaymentMethodConfigurationCreateParams.Grabpay;

      /**
       * iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials. All major Dutch banks are members of Currence, the scheme that operates iDEAL, making it the most popular online payment method in the Netherlands with a share of online transactions close to 55%. Check this [page](https://stripe.com/docs/payments/ideal) for more details.
       */
      ideal?: PaymentMethodConfigurationCreateParams.Ideal;

      /**
       * JCB is a credit card company based in Japan. JCB is currently available in Japan to businesses approved by JCB, and available to all businesses in Australia, Canada, Hong Kong, Japan, New Zealand, Singapore, Switzerland, United Kingdom, United States, and all countries in the European Economic Area except Iceland. Check this [page](https://support.stripe.com/questions/accepting-japan-credit-bureau-%28jcb%29-payments) for more details.
       */
      jcb?: PaymentMethodConfigurationCreateParams.Jcb;

      /**
       * Klarna gives customers a range of [payment options](https://stripe.com/docs/payments/klarna#payment-options) during checkout. Available payment options vary depending on the customer's billing address and the transaction amount. These payment options make it convenient for customers to purchase items in all price ranges. Check this [page](https://stripe.com/docs/payments/klarna) for more details.
       */
      klarna?: PaymentMethodConfigurationCreateParams.Klarna;

      /**
       * Konbini allows customers in Japan to pay for bills and online purchases at convenience stores with cash. Check this [page](https://stripe.com/docs/payments/konbini) for more details.
       */
      konbini?: PaymentMethodConfigurationCreateParams.Konbini;

      /**
       * [Link](https://stripe.com/docs/payments/link) is a payment method network. With Link, users save their payment details once, then reuse that information to pay with one click for any business on the network.
       */
      link?: PaymentMethodConfigurationCreateParams.Link;

      /**
       * MobilePay is a [single-use](https://stripe.com/docs/payments/payment-methods#usage) card wallet payment method used in Denmark and Finland. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the MobilePay app. Check this [page](https://stripe.com/docs/payments/mobilepay) for more details.
       */
      mobilepay?: PaymentMethodConfigurationCreateParams.Mobilepay;

      /**
       * Stripe users in Europe and the United States can accept Multibanco payments from customers in Portugal using [Sources](https://stripe.com/docs/sources)—a single integration path for creating payments using any supported method.
       */
      multibanco?: PaymentMethodConfigurationCreateParams.Multibanco;

      /**
       * Configuration name.
       */
      name?: string;

      /**
       * Stripe users in New Zealand can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with a New Zeland bank account. Check this [page](https://stripe.com/docs/payments/nz-bank-account) for more details.
       */
      nz_bank_account?: PaymentMethodConfigurationCreateParams.NzBankAccount;

      /**
       * OXXO is a Mexican chain of convenience stores with thousands of locations across Latin America and represents nearly 20% of online transactions in Mexico. OXXO allows customers to pay bills and online purchases in-store with cash. Check this [page](https://stripe.com/docs/payments/oxxo) for more details.
       */
      oxxo?: PaymentMethodConfigurationCreateParams.Oxxo;

      /**
       * Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods. Bank transfers account for 30% of online payments in Poland and Przelewy24 provides a way for customers to pay with over 165 banks. Check this [page](https://stripe.com/docs/payments/p24) for more details.
       */
      p24?: PaymentMethodConfigurationCreateParams.P24;

      /**
       * Configuration's parent configuration. Specify to create a child configuration.
       */
      parent?: string;

      /**
       * Pay by bank is a redirect payment method backed by bank transfers. A customer is redirected to their bank to authorize a bank transfer for a given amount. This removes a lot of the error risks inherent in waiting for the customer to initiate a transfer themselves, and is less expensive than card payments.
       */
      pay_by_bank?: PaymentMethodConfigurationCreateParams.PayByBank;

      /**
       * PayNow is a Singapore-based payment method that allows customers to make a payment using their preferred app from participating banks and participating non-bank financial institutions. Check this [page](https://stripe.com/docs/payments/paynow) for more details.
       */
      paynow?: PaymentMethodConfigurationCreateParams.Paynow;

      /**
       * PayPal, a digital wallet popular with customers in Europe, allows your customers worldwide to pay using their PayPal account. Check this [page](https://stripe.com/docs/payments/paypal) for more details.
       */
      paypal?: PaymentMethodConfigurationCreateParams.Paypal;

      /**
       * Pix is a payment method popular in Brazil. When paying with Pix, customers authenticate and approve payments by scanning a QR code in their preferred banking app. Check this [page](https://docs.stripe.com/payments/pix) for more details.
       */
      pix?: PaymentMethodConfigurationCreateParams.Pix;

      /**
       * PromptPay is a Thailand-based payment method that allows customers to make a payment using their preferred app from participating banks. Check this [page](https://stripe.com/docs/payments/promptpay) for more details.
       */
      promptpay?: PaymentMethodConfigurationCreateParams.Promptpay;

      /**
       * Revolut Pay, developed by Revolut, a global finance app, is a digital wallet payment method. Revolut Pay uses the customer's stored balance or cards to fund the payment, and offers the option for non-Revolut customers to save their details after their first purchase.
       */
      revolut_pay?: PaymentMethodConfigurationCreateParams.RevolutPay;

      /**
       * Satispay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method where customers are required to [authenticate](https://stripe.com/payments/payment-methods#customer-actions) their payment. Customers pay by being redirected from your website or app, authorizing the payment with Satispay, then returning to your website or app. You get [immediate notification](https://stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
       */
      satispay?: PaymentMethodConfigurationCreateParams.Satispay;

      /**
       * The [Single Euro Payments Area (SEPA)](https://en.wikipedia.org/wiki/Single_Euro_Payments_Area) is an initiative of the European Union to simplify payments within and across member countries. SEPA established and enforced banking standards to allow for the direct debiting of every EUR-denominated bank account within the SEPA region, check this [page](https://stripe.com/docs/payments/sepa-debit) for more details.
       */
      sepa_debit?: PaymentMethodConfigurationCreateParams.SepaDebit;

      /**
       * Stripe users in Europe and the United States can use the [Payment Intents API](https://stripe.com/docs/payments/payment-intents)—a single integration path for creating payments using any supported method—to accept [Sofort](https://www.sofort.com/) payments from customers. Check this [page](https://stripe.com/docs/payments/sofort) for more details.
       */
      sofort?: PaymentMethodConfigurationCreateParams.Sofort;

      /**
       * Swish is a [real-time](https://stripe.com/docs/payments/real-time) payment method popular in Sweden. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the Swish mobile app and the Swedish BankID mobile app. Check this [page](https://stripe.com/docs/payments/swish) for more details.
       */
      swish?: PaymentMethodConfigurationCreateParams.Swish;

      /**
       * Twint is a payment method popular in Switzerland. It allows customers to pay using their mobile phone. Check this [page](https://docs.stripe.com/payments/twint) for more details.
       */
      twint?: PaymentMethodConfigurationCreateParams.Twint;

      /**
       * Stripe users in the United States can accept ACH direct debit payments from customers with a US bank account using the Automated Clearing House (ACH) payments system operated by Nacha. Check this [page](https://stripe.com/docs/payments/ach-direct-debit) for more details.
       */
      us_bank_account?: PaymentMethodConfigurationCreateParams.UsBankAccount;

      /**
       * WeChat, owned by Tencent, is China's leading mobile app with over 1 billion monthly active users. Chinese consumers can use WeChat Pay to pay for goods and services inside of businesses' apps and websites. WeChat Pay users buy most frequently in gaming, e-commerce, travel, online education, and food/nutrition. Check this [page](https://stripe.com/docs/payments/wechat-pay) for more details.
       */
      wechat_pay?: PaymentMethodConfigurationCreateParams.WechatPay;

      /**
       * Zip gives your customers a way to split purchases over a series of payments. Check this [page](https://stripe.com/docs/payments/zip) for more details like country availability.
       */
      zip?: PaymentMethodConfigurationCreateParams.Zip;
    }

    namespace PaymentMethodConfigurationCreateParams {
      interface AcssDebit {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: AcssDebit.DisplayPreference;
      }

      namespace AcssDebit {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Affirm {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Affirm.DisplayPreference;
      }

      namespace Affirm {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface AfterpayClearpay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: AfterpayClearpay.DisplayPreference;
      }

      namespace AfterpayClearpay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Alipay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Alipay.DisplayPreference;
      }

      namespace Alipay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Alma {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Alma.DisplayPreference;
      }

      namespace Alma {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface AmazonPay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: AmazonPay.DisplayPreference;
      }

      namespace AmazonPay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface ApplePay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: ApplePay.DisplayPreference;
      }

      namespace ApplePay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface ApplePayLater {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: ApplePayLater.DisplayPreference;
      }

      namespace ApplePayLater {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface AuBecsDebit {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: AuBecsDebit.DisplayPreference;
      }

      namespace AuBecsDebit {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface BacsDebit {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: BacsDebit.DisplayPreference;
      }

      namespace BacsDebit {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Bancontact {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Bancontact.DisplayPreference;
      }

      namespace Bancontact {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Billie {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Billie.DisplayPreference;
      }

      namespace Billie {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Blik {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Blik.DisplayPreference;
      }

      namespace Blik {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Boleto {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Boleto.DisplayPreference;
      }

      namespace Boleto {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Card {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Card.DisplayPreference;
      }

      namespace Card {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface CartesBancaires {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: CartesBancaires.DisplayPreference;
      }

      namespace CartesBancaires {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Cashapp {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Cashapp.DisplayPreference;
      }

      namespace Cashapp {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface CustomerBalance {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: CustomerBalance.DisplayPreference;
      }

      namespace CustomerBalance {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Eps {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Eps.DisplayPreference;
      }

      namespace Eps {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Fpx {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Fpx.DisplayPreference;
      }

      namespace Fpx {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Giropay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Giropay.DisplayPreference;
      }

      namespace Giropay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface GooglePay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: GooglePay.DisplayPreference;
      }

      namespace GooglePay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Grabpay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Grabpay.DisplayPreference;
      }

      namespace Grabpay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Ideal {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Ideal.DisplayPreference;
      }

      namespace Ideal {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Jcb {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Jcb.DisplayPreference;
      }

      namespace Jcb {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Klarna {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Klarna.DisplayPreference;
      }

      namespace Klarna {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Konbini {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Konbini.DisplayPreference;
      }

      namespace Konbini {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Link {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Link.DisplayPreference;
      }

      namespace Link {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Mobilepay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Mobilepay.DisplayPreference;
      }

      namespace Mobilepay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Multibanco {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Multibanco.DisplayPreference;
      }

      namespace Multibanco {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface NzBankAccount {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: NzBankAccount.DisplayPreference;
      }

      namespace NzBankAccount {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Oxxo {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Oxxo.DisplayPreference;
      }

      namespace Oxxo {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface P24 {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: P24.DisplayPreference;
      }

      namespace P24 {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface PayByBank {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: PayByBank.DisplayPreference;
      }

      namespace PayByBank {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Paynow {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Paynow.DisplayPreference;
      }

      namespace Paynow {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Paypal {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Paypal.DisplayPreference;
      }

      namespace Paypal {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Pix {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Pix.DisplayPreference;
      }

      namespace Pix {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Promptpay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Promptpay.DisplayPreference;
      }

      namespace Promptpay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface RevolutPay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: RevolutPay.DisplayPreference;
      }

      namespace RevolutPay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Satispay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Satispay.DisplayPreference;
      }

      namespace Satispay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface SepaDebit {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: SepaDebit.DisplayPreference;
      }

      namespace SepaDebit {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Sofort {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Sofort.DisplayPreference;
      }

      namespace Sofort {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Swish {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Swish.DisplayPreference;
      }

      namespace Swish {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Twint {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Twint.DisplayPreference;
      }

      namespace Twint {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface UsBankAccount {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: UsBankAccount.DisplayPreference;
      }

      namespace UsBankAccount {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface WechatPay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: WechatPay.DisplayPreference;
      }

      namespace WechatPay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Zip {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Zip.DisplayPreference;
      }

      namespace Zip {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }
    }

    interface PaymentMethodConfigurationRetrieveParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    interface PaymentMethodConfigurationUpdateParams {
      /**
       * Canadian pre-authorized debit payments, check this [page](https://stripe.com/docs/payments/acss-debit) for more details like country availability.
       */
      acss_debit?: PaymentMethodConfigurationUpdateParams.AcssDebit;

      /**
       * Whether the configuration can be used for new payments.
       */
      active?: boolean;

      /**
       * [Affirm](https://www.affirm.com/) gives your customers a way to split purchases over a series of payments. Depending on the purchase, they can pay with four interest-free payments (Split Pay) or pay over a longer term (Installments), which might include interest. Check this [page](https://stripe.com/docs/payments/affirm) for more details like country availability.
       */
      affirm?: PaymentMethodConfigurationUpdateParams.Affirm;

      /**
       * Afterpay gives your customers a way to pay for purchases in installments, check this [page](https://stripe.com/docs/payments/afterpay-clearpay) for more details like country availability. Afterpay is particularly popular among businesses selling fashion, beauty, and sports products.
       */
      afterpay_clearpay?: PaymentMethodConfigurationUpdateParams.AfterpayClearpay;

      /**
       * Alipay is a digital wallet in China that has more than a billion active users worldwide. Alipay users can pay on the web or on a mobile device using login credentials or their Alipay app. Alipay has a low dispute rate and reduces fraud by authenticating payments using the customer's login credentials. Check this [page](https://stripe.com/docs/payments/alipay) for more details.
       */
      alipay?: PaymentMethodConfigurationUpdateParams.Alipay;

      /**
       * Alma is a Buy Now, Pay Later payment method that offers customers the ability to pay in 2, 3, or 4 installments.
       */
      alma?: PaymentMethodConfigurationUpdateParams.Alma;

      /**
       * Amazon Pay is a wallet payment method that lets your customers check out the same way as on Amazon.
       */
      amazon_pay?: PaymentMethodConfigurationUpdateParams.AmazonPay;

      /**
       * Stripe users can accept [Apple Pay](https://stripe.com/payments/apple-pay) in iOS applications in iOS 9 and later, and on the web in Safari starting with iOS 10 or macOS Sierra. There are no additional fees to process Apple Pay payments, and the [pricing](https://stripe.com/pricing) is the same as other card transactions. Check this [page](https://stripe.com/docs/apple-pay) for more details.
       */
      apple_pay?: PaymentMethodConfigurationUpdateParams.ApplePay;

      /**
       * Apple Pay Later, a payment method for customers to buy now and pay later, gives your customers a way to split purchases into four installments across six weeks.
       */
      apple_pay_later?: PaymentMethodConfigurationUpdateParams.ApplePayLater;

      /**
       * Stripe users in Australia can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with an Australian bank account. Check this [page](https://stripe.com/docs/payments/au-becs-debit) for more details.
       */
      au_becs_debit?: PaymentMethodConfigurationUpdateParams.AuBecsDebit;

      /**
       * Stripe users in the UK can accept Bacs Direct Debit payments from customers with a UK bank account, check this [page](https://stripe.com/docs/payments/payment-methods/bacs-debit) for more details.
       */
      bacs_debit?: PaymentMethodConfigurationUpdateParams.BacsDebit;

      /**
       * Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation. [Customers](https://stripe.com/docs/api/customers) use a Bancontact card or mobile app linked to a Belgian bank account to make online payments that are secure, guaranteed, and confirmed immediately. Check this [page](https://stripe.com/docs/payments/bancontact) for more details.
       */
      bancontact?: PaymentMethodConfigurationUpdateParams.Bancontact;

      /**
       * Billie is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method that offers businesses Pay by Invoice where they offer payment terms ranging from 7-120 days. Customers are redirected from your website or app, authorize the payment with Billie, then return to your website or app. You get [immediate notification](https://stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
       */
      billie?: PaymentMethodConfigurationUpdateParams.Billie;

      /**
       * BLIK is a [single use](https://stripe.com/docs/payments/payment-methods#usage) payment method that requires customers to authenticate their payments. When customers want to pay online using BLIK, they request a six-digit code from their banking application and enter it into the payment collection form. Check this [page](https://stripe.com/docs/payments/blik) for more details.
       */
      blik?: PaymentMethodConfigurationUpdateParams.Blik;

      /**
       * Boleto is an official (regulated by the Central Bank of Brazil) payment method in Brazil. Check this [page](https://stripe.com/docs/payments/boleto) for more details.
       */
      boleto?: PaymentMethodConfigurationUpdateParams.Boleto;

      /**
       * Cards are a popular way for consumers and businesses to pay online or in person. Stripe supports global and local card networks.
       */
      card?: PaymentMethodConfigurationUpdateParams.Card;

      /**
       * Cartes Bancaires is France's local card network. More than 95% of these cards are co-branded with either Visa or Mastercard, meaning you can process these cards over either Cartes Bancaires or the Visa or Mastercard networks. Check this [page](https://stripe.com/docs/payments/cartes-bancaires) for more details.
       */
      cartes_bancaires?: PaymentMethodConfigurationUpdateParams.CartesBancaires;

      /**
       * Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet. Check this [page](https://stripe.com/docs/payments/cash-app-pay) for more details.
       */
      cashapp?: PaymentMethodConfigurationUpdateParams.Cashapp;

      /**
       * Uses a customer's [cash balance](https://stripe.com/docs/payments/customer-balance) for the payment. The cash balance can be funded via a bank transfer. Check this [page](https://stripe.com/docs/payments/bank-transfers) for more details.
       */
      customer_balance?: PaymentMethodConfigurationUpdateParams.CustomerBalance;

      /**
       * EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials. EPS is supported by all Austrian banks and is accepted by over 80% of Austrian online retailers. Check this [page](https://stripe.com/docs/payments/eps) for more details.
       */
      eps?: PaymentMethodConfigurationUpdateParams.Eps;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;

      /**
       * Financial Process Exchange (FPX) is a Malaysia-based payment method that allows customers to complete transactions online using their bank credentials. Bank Negara Malaysia (BNM), the Central Bank of Malaysia, and eleven other major Malaysian financial institutions are members of the PayNet Group, which owns and operates FPX. It is one of the most popular online payment methods in Malaysia, with nearly 90 million transactions in 2018 according to BNM. Check this [page](https://stripe.com/docs/payments/fpx) for more details.
       */
      fpx?: PaymentMethodConfigurationUpdateParams.Fpx;

      /**
       * giropay is a German payment method based on online banking, introduced in 2006. It allows customers to complete transactions online using their online banking environment, with funds debited from their bank account. Depending on their bank, customers confirm payments on giropay using a second factor of authentication or a PIN. giropay accounts for 10% of online checkouts in Germany. Check this [page](https://stripe.com/docs/payments/giropay) for more details.
       */
      giropay?: PaymentMethodConfigurationUpdateParams.Giropay;

      /**
       * Google Pay allows customers to make payments in your app or website using any credit or debit card saved to their Google Account, including those from Google Play, YouTube, Chrome, or an Android device. Use the Google Pay API to request any credit or debit card stored in your customer's Google account. Check this [page](https://stripe.com/docs/google-pay) for more details.
       */
      google_pay?: PaymentMethodConfigurationUpdateParams.GooglePay;

      /**
       * GrabPay is a payment method developed by [Grab](https://www.grab.com/sg/consumer/finance/pay/). GrabPay is a digital wallet - customers maintain a balance in their wallets that they pay out with. Check this [page](https://stripe.com/docs/payments/grabpay) for more details.
       */
      grabpay?: PaymentMethodConfigurationUpdateParams.Grabpay;

      /**
       * iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials. All major Dutch banks are members of Currence, the scheme that operates iDEAL, making it the most popular online payment method in the Netherlands with a share of online transactions close to 55%. Check this [page](https://stripe.com/docs/payments/ideal) for more details.
       */
      ideal?: PaymentMethodConfigurationUpdateParams.Ideal;

      /**
       * JCB is a credit card company based in Japan. JCB is currently available in Japan to businesses approved by JCB, and available to all businesses in Australia, Canada, Hong Kong, Japan, New Zealand, Singapore, Switzerland, United Kingdom, United States, and all countries in the European Economic Area except Iceland. Check this [page](https://support.stripe.com/questions/accepting-japan-credit-bureau-%28jcb%29-payments) for more details.
       */
      jcb?: PaymentMethodConfigurationUpdateParams.Jcb;

      /**
       * Klarna gives customers a range of [payment options](https://stripe.com/docs/payments/klarna#payment-options) during checkout. Available payment options vary depending on the customer's billing address and the transaction amount. These payment options make it convenient for customers to purchase items in all price ranges. Check this [page](https://stripe.com/docs/payments/klarna) for more details.
       */
      klarna?: PaymentMethodConfigurationUpdateParams.Klarna;

      /**
       * Konbini allows customers in Japan to pay for bills and online purchases at convenience stores with cash. Check this [page](https://stripe.com/docs/payments/konbini) for more details.
       */
      konbini?: PaymentMethodConfigurationUpdateParams.Konbini;

      /**
       * [Link](https://stripe.com/docs/payments/link) is a payment method network. With Link, users save their payment details once, then reuse that information to pay with one click for any business on the network.
       */
      link?: PaymentMethodConfigurationUpdateParams.Link;

      /**
       * MobilePay is a [single-use](https://stripe.com/docs/payments/payment-methods#usage) card wallet payment method used in Denmark and Finland. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the MobilePay app. Check this [page](https://stripe.com/docs/payments/mobilepay) for more details.
       */
      mobilepay?: PaymentMethodConfigurationUpdateParams.Mobilepay;

      /**
       * Stripe users in Europe and the United States can accept Multibanco payments from customers in Portugal using [Sources](https://stripe.com/docs/sources)—a single integration path for creating payments using any supported method.
       */
      multibanco?: PaymentMethodConfigurationUpdateParams.Multibanco;

      /**
       * Configuration name.
       */
      name?: string;

      /**
       * Stripe users in New Zealand can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with a New Zeland bank account. Check this [page](https://stripe.com/docs/payments/nz-bank-account) for more details.
       */
      nz_bank_account?: PaymentMethodConfigurationUpdateParams.NzBankAccount;

      /**
       * OXXO is a Mexican chain of convenience stores with thousands of locations across Latin America and represents nearly 20% of online transactions in Mexico. OXXO allows customers to pay bills and online purchases in-store with cash. Check this [page](https://stripe.com/docs/payments/oxxo) for more details.
       */
      oxxo?: PaymentMethodConfigurationUpdateParams.Oxxo;

      /**
       * Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods. Bank transfers account for 30% of online payments in Poland and Przelewy24 provides a way for customers to pay with over 165 banks. Check this [page](https://stripe.com/docs/payments/p24) for more details.
       */
      p24?: PaymentMethodConfigurationUpdateParams.P24;

      /**
       * Pay by bank is a redirect payment method backed by bank transfers. A customer is redirected to their bank to authorize a bank transfer for a given amount. This removes a lot of the error risks inherent in waiting for the customer to initiate a transfer themselves, and is less expensive than card payments.
       */
      pay_by_bank?: PaymentMethodConfigurationUpdateParams.PayByBank;

      /**
       * PayNow is a Singapore-based payment method that allows customers to make a payment using their preferred app from participating banks and participating non-bank financial institutions. Check this [page](https://stripe.com/docs/payments/paynow) for more details.
       */
      paynow?: PaymentMethodConfigurationUpdateParams.Paynow;

      /**
       * PayPal, a digital wallet popular with customers in Europe, allows your customers worldwide to pay using their PayPal account. Check this [page](https://stripe.com/docs/payments/paypal) for more details.
       */
      paypal?: PaymentMethodConfigurationUpdateParams.Paypal;

      /**
       * Pix is a payment method popular in Brazil. When paying with Pix, customers authenticate and approve payments by scanning a QR code in their preferred banking app. Check this [page](https://docs.stripe.com/payments/pix) for more details.
       */
      pix?: PaymentMethodConfigurationUpdateParams.Pix;

      /**
       * PromptPay is a Thailand-based payment method that allows customers to make a payment using their preferred app from participating banks. Check this [page](https://stripe.com/docs/payments/promptpay) for more details.
       */
      promptpay?: PaymentMethodConfigurationUpdateParams.Promptpay;

      /**
       * Revolut Pay, developed by Revolut, a global finance app, is a digital wallet payment method. Revolut Pay uses the customer's stored balance or cards to fund the payment, and offers the option for non-Revolut customers to save their details after their first purchase.
       */
      revolut_pay?: PaymentMethodConfigurationUpdateParams.RevolutPay;

      /**
       * Satispay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method where customers are required to [authenticate](https://stripe.com/payments/payment-methods#customer-actions) their payment. Customers pay by being redirected from your website or app, authorizing the payment with Satispay, then returning to your website or app. You get [immediate notification](https://stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
       */
      satispay?: PaymentMethodConfigurationUpdateParams.Satispay;

      /**
       * The [Single Euro Payments Area (SEPA)](https://en.wikipedia.org/wiki/Single_Euro_Payments_Area) is an initiative of the European Union to simplify payments within and across member countries. SEPA established and enforced banking standards to allow for the direct debiting of every EUR-denominated bank account within the SEPA region, check this [page](https://stripe.com/docs/payments/sepa-debit) for more details.
       */
      sepa_debit?: PaymentMethodConfigurationUpdateParams.SepaDebit;

      /**
       * Stripe users in Europe and the United States can use the [Payment Intents API](https://stripe.com/docs/payments/payment-intents)—a single integration path for creating payments using any supported method—to accept [Sofort](https://www.sofort.com/) payments from customers. Check this [page](https://stripe.com/docs/payments/sofort) for more details.
       */
      sofort?: PaymentMethodConfigurationUpdateParams.Sofort;

      /**
       * Swish is a [real-time](https://stripe.com/docs/payments/real-time) payment method popular in Sweden. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the Swish mobile app and the Swedish BankID mobile app. Check this [page](https://stripe.com/docs/payments/swish) for more details.
       */
      swish?: PaymentMethodConfigurationUpdateParams.Swish;

      /**
       * Twint is a payment method popular in Switzerland. It allows customers to pay using their mobile phone. Check this [page](https://docs.stripe.com/payments/twint) for more details.
       */
      twint?: PaymentMethodConfigurationUpdateParams.Twint;

      /**
       * Stripe users in the United States can accept ACH direct debit payments from customers with a US bank account using the Automated Clearing House (ACH) payments system operated by Nacha. Check this [page](https://stripe.com/docs/payments/ach-direct-debit) for more details.
       */
      us_bank_account?: PaymentMethodConfigurationUpdateParams.UsBankAccount;

      /**
       * WeChat, owned by Tencent, is China's leading mobile app with over 1 billion monthly active users. Chinese consumers can use WeChat Pay to pay for goods and services inside of businesses' apps and websites. WeChat Pay users buy most frequently in gaming, e-commerce, travel, online education, and food/nutrition. Check this [page](https://stripe.com/docs/payments/wechat-pay) for more details.
       */
      wechat_pay?: PaymentMethodConfigurationUpdateParams.WechatPay;

      /**
       * Zip gives your customers a way to split purchases over a series of payments. Check this [page](https://stripe.com/docs/payments/zip) for more details like country availability.
       */
      zip?: PaymentMethodConfigurationUpdateParams.Zip;
    }

    namespace PaymentMethodConfigurationUpdateParams {
      interface AcssDebit {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: AcssDebit.DisplayPreference;
      }

      namespace AcssDebit {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Affirm {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Affirm.DisplayPreference;
      }

      namespace Affirm {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface AfterpayClearpay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: AfterpayClearpay.DisplayPreference;
      }

      namespace AfterpayClearpay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Alipay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Alipay.DisplayPreference;
      }

      namespace Alipay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Alma {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Alma.DisplayPreference;
      }

      namespace Alma {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface AmazonPay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: AmazonPay.DisplayPreference;
      }

      namespace AmazonPay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface ApplePay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: ApplePay.DisplayPreference;
      }

      namespace ApplePay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface ApplePayLater {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: ApplePayLater.DisplayPreference;
      }

      namespace ApplePayLater {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface AuBecsDebit {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: AuBecsDebit.DisplayPreference;
      }

      namespace AuBecsDebit {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface BacsDebit {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: BacsDebit.DisplayPreference;
      }

      namespace BacsDebit {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Bancontact {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Bancontact.DisplayPreference;
      }

      namespace Bancontact {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Billie {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Billie.DisplayPreference;
      }

      namespace Billie {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Blik {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Blik.DisplayPreference;
      }

      namespace Blik {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Boleto {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Boleto.DisplayPreference;
      }

      namespace Boleto {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Card {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Card.DisplayPreference;
      }

      namespace Card {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface CartesBancaires {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: CartesBancaires.DisplayPreference;
      }

      namespace CartesBancaires {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Cashapp {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Cashapp.DisplayPreference;
      }

      namespace Cashapp {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface CustomerBalance {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: CustomerBalance.DisplayPreference;
      }

      namespace CustomerBalance {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Eps {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Eps.DisplayPreference;
      }

      namespace Eps {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Fpx {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Fpx.DisplayPreference;
      }

      namespace Fpx {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Giropay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Giropay.DisplayPreference;
      }

      namespace Giropay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface GooglePay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: GooglePay.DisplayPreference;
      }

      namespace GooglePay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Grabpay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Grabpay.DisplayPreference;
      }

      namespace Grabpay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Ideal {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Ideal.DisplayPreference;
      }

      namespace Ideal {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Jcb {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Jcb.DisplayPreference;
      }

      namespace Jcb {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Klarna {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Klarna.DisplayPreference;
      }

      namespace Klarna {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Konbini {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Konbini.DisplayPreference;
      }

      namespace Konbini {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Link {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Link.DisplayPreference;
      }

      namespace Link {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Mobilepay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Mobilepay.DisplayPreference;
      }

      namespace Mobilepay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Multibanco {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Multibanco.DisplayPreference;
      }

      namespace Multibanco {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface NzBankAccount {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: NzBankAccount.DisplayPreference;
      }

      namespace NzBankAccount {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Oxxo {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Oxxo.DisplayPreference;
      }

      namespace Oxxo {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface P24 {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: P24.DisplayPreference;
      }

      namespace P24 {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface PayByBank {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: PayByBank.DisplayPreference;
      }

      namespace PayByBank {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Paynow {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Paynow.DisplayPreference;
      }

      namespace Paynow {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Paypal {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Paypal.DisplayPreference;
      }

      namespace Paypal {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Pix {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Pix.DisplayPreference;
      }

      namespace Pix {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Promptpay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Promptpay.DisplayPreference;
      }

      namespace Promptpay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface RevolutPay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: RevolutPay.DisplayPreference;
      }

      namespace RevolutPay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Satispay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Satispay.DisplayPreference;
      }

      namespace Satispay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface SepaDebit {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: SepaDebit.DisplayPreference;
      }

      namespace SepaDebit {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Sofort {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Sofort.DisplayPreference;
      }

      namespace Sofort {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Swish {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Swish.DisplayPreference;
      }

      namespace Swish {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Twint {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Twint.DisplayPreference;
      }

      namespace Twint {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface UsBankAccount {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: UsBankAccount.DisplayPreference;
      }

      namespace UsBankAccount {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface WechatPay {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: WechatPay.DisplayPreference;
      }

      namespace WechatPay {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }

      interface Zip {
        /**
         * Whether or not the payment method should be displayed.
         */
        display_preference?: Zip.DisplayPreference;
      }

      namespace Zip {
        interface DisplayPreference {
          /**
           * The account's preference for whether or not to display this payment method.
           */
          preference?: DisplayPreference.Preference;
        }

        namespace DisplayPreference {
          type Preference = 'none' | 'off' | 'on';
        }
      }
    }

    interface PaymentMethodConfigurationListParams extends PaginationParams {
      /**
       * The Connect application to filter by.
       */
      application?: Stripe.Emptyable<string>;

      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    class PaymentMethodConfigurationsResource {
      /**
       * Creates a payment method configuration
       */
      create(
        params?: PaymentMethodConfigurationCreateParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethodConfiguration>>;
      create(
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethodConfiguration>>;

      /**
       * Retrieve payment method configuration
       */
      retrieve(
        id: string,
        params?: PaymentMethodConfigurationRetrieveParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethodConfiguration>>;
      retrieve(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethodConfiguration>>;

      /**
       * Update payment method configuration
       */
      update(
        id: string,
        params?: PaymentMethodConfigurationUpdateParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.PaymentMethodConfiguration>>;

      /**
       * List payment method configurations
       */
      list(
        params?: PaymentMethodConfigurationListParams,
        options?: RequestOptions
      ): ApiListPromise<Stripe.PaymentMethodConfiguration>;
      list(
        options?: RequestOptions
      ): ApiListPromise<Stripe.PaymentMethodConfiguration>;
    }
  }
}
