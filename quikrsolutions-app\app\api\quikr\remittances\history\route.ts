import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { Database } from '@/types/supabase';

type RemittanceTransactionRow = Database['public']['Tables']['remittance_transactions']['Row'];

export interface RemittanceHistoryResponse {
  status: 'success';
  data: RemittanceTransactionRow[];
}

export interface RemittanceHistoryErrorResponse {
  status: 'error';
  message: string;
  details?: Record<string, any>;
}

export async function GET(request: Request) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { 
          status: 'error', 
          message: 'Authentication required',
          details: { authError: authError?.message }
        } as RemittanceHistoryErrorResponse,
        { status: 401 }
      );
    }

    // Fetch remittance transactions for the current user
    const { data: remittances, error: fetchError } = await supabase
      .from('remittance_transactions')
      .select('*')
      .eq('sender_user_id', user.id)
      .order('created_at', { ascending: false });

    if (fetchError) {
      console.error('Error fetching remittance history:', fetchError);
      return NextResponse.json(
        { 
          status: 'error', 
          message: 'Failed to fetch remittance history',
          details: { fetchError: fetchError.message }
        } as RemittanceHistoryErrorResponse,
        { status: 500 }
      );
    }

    return NextResponse.json({
      status: 'success',
      data: remittances || []
    } as RemittanceHistoryResponse);

  } catch (error: any) {
    console.error('Unexpected error in remittance history API:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'An unexpected error occurred',
        details: { error: error.message }
      } as RemittanceHistoryErrorResponse,
      { status: 500 }
    );
  }
}
