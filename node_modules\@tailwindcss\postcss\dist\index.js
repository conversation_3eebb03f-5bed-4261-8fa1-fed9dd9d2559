"use strict";var V=Object.create;var _=Object.defineProperty;var Z=Object.getOwnPropertyDescriptor;var ee=Object.getOwnPropertyNames;var te=Object.getPrototypeOf,se=Object.prototype.hasOwnProperty;var B=(o,i)=>(i=Symbol[o])?i:Symbol.for("Symbol."+o),U=o=>{throw TypeError(o)};var ie=(o,i,r,e)=>{if(i&&typeof i=="object"||typeof i=="function")for(let s of ee(i))!se.call(o,s)&&s!==r&&_(o,s,{get:()=>i[s],enumerable:!(e=Z(i,s))||e.enumerable});return o};var h=(o,i,r)=>(r=o!=null?V(te(o)):{},ie(i||!o||!o.__esModule?_(r,"default",{value:o,enumerable:!0}):r,o));var D=(o,i,r)=>{if(i!=null){typeof i!="object"&&typeof i!="function"&&U("Object expected");var e,s;r&&(e=i[B("asyncDispose")]),e===void 0&&(e=i[B("dispose")],r&&(s=e)),typeof e!="function"&&U("Object not disposable"),s&&(e=function(){try{s.call(this)}catch(t){return Promise.reject(t)}}),o.push([r,e,i])}else r&&o.push([r]);return i},z=(o,i,r)=>{var e=typeof SuppressedError=="function"?SuppressedError:function(n,l,u,p){return p=Error(u),p.name="SuppressedError",p.error=n,p.suppressed=l,p},s=n=>i=r?new e(n,i,"An error was suppressed during disposal"):(r=!0,n),t=n=>{for(;n=o.pop();)try{var l=n[1]&&n[1].call(n[2]);if(n[0])return Promise.resolve(l).then(t,u=>(s(u),t()))}catch(u){s(u)}if(r)throw i};return t()};var K=h(require("@alloc/quick-lru")),m=require("@tailwindcss/node"),Q=require("@tailwindcss/node/require-cache"),j=require("@tailwindcss/oxide"),g=require("lightningcss"),F=h(require("fs")),d=h(require("path")),P=h(require("postcss"));var re=32;var oe=40;function M(o,i=[]){for(let r=5;r<o.length;r++){let e=o.charCodeAt(r);if(e===re||e===oe){let s=o.slice(0,r).trim(),t=o.slice(r).trim();return x(s,t,i)}}return x(o.trim(),"",i)}var ne=64;function le(o,i=[]){return{kind:"rule",selector:o,nodes:i}}function x(o,i="",r=[]){return{kind:"at-rule",name:o,params:i,nodes:r}}function R(o,i=[]){return o.charCodeAt(0)===ne?M(o,i):le(o,i)}function w(o,i,r=!1){return{kind:"declaration",property:o,value:i,important:r}}function b(o){return{kind:"comment",value:o}}function I(o){function i(e,s=0){let t="",n="  ".repeat(s);if(e.kind==="declaration")t+=`${n}${e.property}: ${e.value}${e.important?" !important":""};
`;else if(e.kind==="rule"){t+=`${n}${e.selector} {
`;for(let l of e.nodes)t+=i(l,s+1);t+=`${n}}
`}else if(e.kind==="at-rule"){if(e.nodes.length===0)return`${n}${e.name} ${e.params};
`;t+=`${n}${e.name}${e.params?` ${e.params} `:" "}{
`;for(let l of e.nodes)t+=i(l,s+1);t+=`${n}}
`}else if(e.kind==="comment")t+=`${n}/*${e.value}*/
`;else if(e.kind==="context"||e.kind==="at-root")return"";return t}let r="";for(let e of o){let s=i(e);s!==""&&(r+=s)}return r}var C=h(require("postcss"));var ae=33;function W(o,i){let r=C.default.root();r.source=i;function e(s,t){if(s.kind==="declaration"){let n=C.default.decl({prop:s.property,value:s.value??"",important:s.important});n.source=i,t.append(n)}else if(s.kind==="rule"){let n=C.default.rule({selector:s.selector});n.source=i,n.raws.semicolon=!0,t.append(n);for(let l of s.nodes)e(l,n)}else if(s.kind==="at-rule"){let n=C.default.atRule({name:s.name.slice(1),params:s.params});n.source=i,n.raws.semicolon=!0,t.append(n);for(let l of s.nodes)e(l,n)}else if(s.kind==="comment"){let n=C.default.comment({text:s.value});n.raws.left="",n.raws.right="",n.source=i,t.append(n)}else s.kind==="at-root"||s.kind}for(let s of o)e(s,r);return r}function G(o){function i(e,s){if(e.type==="decl")s.push(w(e.prop,e.value,e.important));else if(e.type==="rule"){let t=R(e.selector);e.each(n=>i(n,t.nodes)),s.push(t)}else if(e.type==="atrule"){let t=x(`@${e.name}`,e.params);e.each(n=>i(n,t.nodes)),s.push(t)}else if(e.type==="comment"){if(e.text.charCodeAt(0)!==ae)return;s.push(b(e.text))}}let r=[];return o.each(e=>i(e,r)),r}var v=require("@tailwindcss/node"),y=h(require("path")),E="'",T='"';function O(){let o=new WeakSet;function i(r){let e=r.root().source?.input.file;if(!e)return;let s=r.source?.input.file;if(!s||o.has(r))return;let t=r.params[0],n=t[0]===T&&t[t.length-1]===T?T:t[0]===E&&t[t.length-1]===E?E:null;if(!n)return;let l=r.params.slice(1,-1),u="";if(l.startsWith("!")&&(l=l.slice(1),u="!"),!l.startsWith("./")&&!l.startsWith("../"))return;let p=y.default.posix.join((0,v.normalizePath)(y.default.dirname(s)),l),k=y.default.posix.dirname((0,v.normalizePath)(e)),A=y.default.posix.relative(k,p);A.startsWith(".")||(A="./"+A),r.params=n+u+A+n,o.add(r)}return{postcssPlugin:"tailwindcss-postcss-fix-relative-paths",Once(r){r.walkAtRules(/source|plugin|config/,i)}}}var a=m.env.DEBUG,$=new K.default({maxSize:50});function ce(o,i){let r=`${o}:${i.base??""}:${JSON.stringify(i.optimize)}`;if($.has(r))return $.get(r);let e={mtimes:new Map,compiler:null,scanner:null,tailwindCssAst:[],cachedPostCssAst:P.default.root(),optimizedPostCssAst:P.default.root(),fullRebuildPaths:[]};return $.set(r,e),e}function fe(o={}){let i=o.base??process.cwd(),r=o.optimize??process.env.NODE_ENV==="production";return{postcssPlugin:"@tailwindcss/postcss",plugins:[O(),{postcssPlugin:"tailwindcss",async Once(e,{result:s}){var L=[];try{let t=D(L,new m.Instrumentation);let n=s.opts.from??"";a&&t.start(`[@tailwindcss/postcss] ${(0,d.relative)(i,n)}`);{a&&t.start("Quick bail check");let f=!0;if(e.walkAtRules(c=>{if(c.name==="import"||c.name==="theme"||c.name==="config"||c.name==="plugin"||c.name==="apply")return f=!1,!1}),f)return;a&&t.end("Quick bail check")}let l=ce(n,o);let u=d.default.dirname(d.default.resolve(n));async function p(){a&&t.start("Setup compiler"),l.fullRebuildPaths.length>0&&!k&&(0,Q.clearRequireCache)(l.fullRebuildPaths),l.fullRebuildPaths=[],a&&t.start("PostCSS AST -> Tailwind CSS AST");let f=G(e);a&&t.end("PostCSS AST -> Tailwind CSS AST"),a&&t.start("Create compiler");let c=await(0,m.compileAst)(f,{base:u,onDependency:S=>{l.fullRebuildPaths.push(S)}});return a&&t.end("Create compiler"),a&&t.end("Setup compiler"),c}let k=l.compiler===null;l.compiler??=await p();if(l.compiler.features===m.Features.None)return;let A="incremental";a&&t.start("Register full rebuild paths");{for(let c of l.fullRebuildPaths)s.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:d.default.resolve(c),parent:s.opts.from});let f=s.messages.flatMap(c=>c.type!=="dependency"?[]:c.file);f.push(n);for(let c of f){let S=F.default.statSync(c,{throwIfNoEntry:!1})?.mtimeMs??null;if(S===null){c===n&&(A="full");continue}l.mtimes.get(c)!==S&&(A="full",l.mtimes.set(c,S))}}a&&t.end("Register full rebuild paths");A==="full"&&!k&&(l.compiler=await p());if(l.scanner===null||A==="full"){a&&t.start("Setup scanner");let f=(l.compiler.root==="none"?[]:l.compiler.root===null?[{base:i,pattern:"**/*"}]:[l.compiler.root]).concat(l.compiler.globs);l.scanner=new j.Scanner({sources:f}),a&&t.end("Setup scanner")}a&&t.start("Scan for candidates");let q=l.compiler.features&m.Features.Utilities?l.scanner.scan():[];a&&t.end("Scan for candidates");if(l.compiler.features&m.Features.Utilities){a&&t.start("Register dependency messages");for(let f of l.scanner.files)s.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:d.default.resolve(f),parent:s.opts.from});for(let{base:f,pattern:c}of l.scanner.globs)c==="*"&&i===f||(c===""?s.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:d.default.resolve(f),parent:s.opts.from}):s.messages.push({type:"dir-dependency",plugin:"@tailwindcss/postcss",dir:d.default.resolve(f),glob:c,parent:s.opts.from}));a&&t.end("Register dependency messages")}a&&t.start("Build utilities");let N=l.compiler.build(q);a&&t.end("Build utilities");if(l.tailwindCssAst!==N)if(r){a&&t.start("Optimization"),a&&t.start("AST -> CSS");let f=I(N);a&&t.end("AST -> CSS"),a&&t.start("Lightning CSS");let c=ue(f,{minify:typeof r=="object"?r.minify:!0});a&&t.end("Lightning CSS"),a&&t.start("CSS -> PostCSS AST"),l.optimizedPostCssAst=P.default.parse(c,s.opts),a&&t.end("CSS -> PostCSS AST"),a&&t.end("Optimization")}else a&&t.start("Transform Tailwind CSS AST into PostCSS AST"),l.cachedPostCssAst=W(N,e.source),a&&t.end("Transform Tailwind CSS AST into PostCSS AST");l.tailwindCssAst=N;a&&t.start("Update PostCSS AST");e.removeAll();e.append(r?l.optimizedPostCssAst.clone().nodes:l.cachedPostCssAst.clone().nodes);e.raws.indent="  ";a&&t.end("Update PostCSS AST");a&&t.end(`[@tailwindcss/postcss] ${(0,d.relative)(i,n)}`)}catch(X){var Y=X,J=!0}finally{z(L,Y,J)}}}]}}function ue(o,{file:i="input.css",minify:r=!1}={}){function e(s){return(0,g.transform)({filename:i,code:s,minify:r,sourceMap:!1,drafts:{customMedia:!0},nonStandard:{deepSelectorCombinator:!0},include:g.Features.Nesting,exclude:g.Features.LogicalProperties|g.Features.DirSelector|g.Features.LightDark,targets:{safari:16<<16|1024,ios_saf:16<<16|1024,firefox:8388608,chrome:7274496},errorRecovery:!0}).code}return e(e(Buffer.from(o))).toString()}var H=Object.assign(fe,{postcss:!0});module.exports=H;
