import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { Database } from '@/types/supabase';
import { v4 as uuidv4 } from 'uuid';
import { initiateRemittance } from '@/lib/services/eloh/elohRemittanceService';
import { InitiateElohRemittanceParams } from '@/lib/services/eloh/elohTypes';

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Extract remittance details from the request body
    const {
      userId, // Sender ID
      recipientFirstName,
      recipientLastName,
      recipientCountry,
      recipientPhoneNumber, // Assuming these are passed in the body
      recipientEmail,       // Assuming these are passed in the body
      recipientStellarAddress, // Assuming these are passed in the body
      recipientBankName,      // Assuming these are passed in the body
      recipientBankAccountNumber, // Assuming these are passed in the body
      recipientPickupLocationId, // Assuming these are passed in the body
      sendAmount,
      sendCurrency,
      receiveCurrency, // We only need receiveCurrency here, amount will be calculated by Eloh or from quote
      quoteId, // Optional quote ID
      purposeOfRemittance, // Optional purpose
    } = body;

    // Basic validation (add more comprehensive validation as needed)
    if (!userId || !recipientFirstName || !recipientLastName || !recipientCountry || !sendAmount || !sendCurrency || !receiveCurrency || !quoteId) {
      console.error('Invalid remittance request parameters received:', body);
      return NextResponse.json({ status: 'error', message: 'Invalid request parameters or missing quoteId' }, { status: 400 });
    }

    const supabaseAdmin = createSupabaseAdminClient();

    // Retrieve quote details from the database
    const { data: quoteData, error: quoteError } = await supabaseAdmin
      .from('eloh_exchange_rate_quotes')
      .select('*')
      .eq('eloh_quote_id', quoteId)
      .single();

    if (quoteError || !quoteData) {
      console.error('Error retrieving quote details:', quoteError);
      return NextResponse.json({ status: 'error', message: 'Invalid or expired quote ID' }, { status: 400 });
    }

    // Check if the quote has expired
    if (new Date() > new Date(quoteData.expires_at)) {
       console.error('Quote has expired:', quoteId);
       return NextResponse.json({ status: 'error', message: 'Quote has expired' }, { status: 400 });
    }

    // Generate a unique internal remittance ID
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const quikrsolutionsRemittanceId = `QSRMT-${timestamp}-${randomString}`;

    // Insert the initial remittance record into the database using quote details
    const insertPayload: Database['public']['Tables']['remittance_transactions']['Insert'] = {
      user_id: userId,
      recipient_name: `${recipientFirstName} ${recipientLastName}`, // Combine name for initial record
      recipient_contact: recipientPhoneNumber || recipientEmail, // Use one of the contacts
      recipient_country: recipientCountry,
      send_amount: sendAmount,
      send_currency: sendCurrency,
      receive_amount: quoteData.destination_amount, // Use receive amount from quote
      receive_currency: receiveCurrency,
      exchange_rate: quoteData.rate, // Use exchange rate from quote
      fees: quoteData.fee, // Use fees from quote
      quikrsolutions_remittance_id: quikrsolutionsRemittanceId,
      status: 'pending', // Initial status before Eloh interaction
    };

    console.log('Attempting to insert initial remittance record with payload:', insertPayload);

    const { data: insertData, error: insertError } = await supabaseAdmin
      .from('remittance_transactions')
      .insert([insertPayload]).select().single();

    if (insertError) {
      console.error('Error inserting initial remittance record:', insertError);
      return NextResponse.json({ status: 'error', message: 'Failed to create remittance record', details: insertError.message }, { status: 500 });
    }

    console.log('Inserted initial remittance record:', insertData);

    // Prepare parameters for the Eloh service
    const elohRemittanceParams: InitiateElohRemittanceParams = {
      quoteId: quoteId, // Pass the quoteId to Eloh
      amountToSend: sendAmount,
      sendCurrency: sendCurrency,
      receiveCurrency: receiveCurrency,
      senderUserId: userId, // Pass Quikrsolutions user ID
      recipientFirstName: recipientFirstName,
      recipientLastName: recipientLastName,
      recipientCountry: recipientCountry,
      recipientPhoneNumber: recipientPhoneNumber,
      recipientEmail: recipientEmail,
      recipientStellarAddress: recipientStellarAddress,
      recipientBankName: recipientBankName,
      recipientBankAccountNumber: recipientBankAccountNumber,
      recipientPickupLocationId: recipientPickupLocationId,
      quikrsolutionsRemittanceId: quikrsolutionsRemittanceId,
      purposeOfRemittance: purposeOfRemittance,
    };

    console.log('Eloh Remittance Params:', elohRemittanceParams);
    // Call the Eloh remittance service
    const elohResult = await initiateRemittance(elohRemittanceParams);

    console.log('Eloh Remittance Result:', elohResult);

    // Update the database record with details from the Eloh service response
    if (elohResult.status === 'success') {
      const { elohRemittanceId, currentStatus, stellarTransactionId, estimatedDeliveryAt } = elohResult.data;

      const updatePayload: Partial<Database['public']['Tables']['remittance_transactions']['Update']> = {
        eloh_remittance_id: elohRemittanceId,
        stellar_transaction_hash: stellarTransactionId, // Use stellarTransactionId from Eloh response
        status: currentStatus, // Update status based on Eloh response
        // Add estimated_delivery_at if your DB schema supports it
        // estimated_delivery_at: estimatedDeliveryAt ? new Date(estimatedDeliveryAt).toISOString() : null,
      };

      const { data: updateData, error: updateError } = await supabaseAdmin
        .from('remittance_transactions')
        .update(updatePayload)
        .eq('id', insertData.id).select().single();

      if (updateError) {
        console.error('Error updating remittance record with Eloh details:', updateError);
        // Decide how to handle this error - maybe log and proceed, or return an error to the user
      }

      console.log('Updated remittance record with Eloh details:', updateData);

      // Return success response to the frontend
      return NextResponse.json({
        status: 'success',
        data: {
          quikrsolutionsRemittanceId: quikrsolutionsRemittanceId,
          elohRemittanceId: elohRemittanceId,
          stellarTransactionId: stellarTransactionId,
          status: currentStatus,
          // Include other relevant data from Eloh response if needed
          // like estimatedReceiveAmount, exchangeRate, etc.
        },
      }, { status: 200 });

    } else {
      // Handle Eloh API error
      console.error('Eloh Remittance service call failed:', elohResult);
      // Update the database record to reflect the failure
      await supabaseAdmin
        .from('remittance_transactions')
        .update({ status: 'eloh_api_error', error_details: elohResult.message }) // Define a specific status for this
        .eq('id', insertData.id);

      return NextResponse.json({ status: 'error', message: elohResult.message, details: elohResult.details }, { status: 500 });
    }

  } catch (error: any) {
    console.error('Error processing remittance request:', error);
    return NextResponse.json({ status: 'error', message: 'Internal server error', details: error.message }, { status: 500 });
  }
}
