/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/quikr/remittances/history/route";
exports.ids = ["app/api/quikr/remittances/history/route"];
exports.modules = {

/***/ "(rsc)/./app/api/quikr/remittances/history/route.ts":
/*!****************************************************!*\
  !*** ./app/api/quikr/remittances/history/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClient)();\n        // Get the current user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Authentication required',\n                details: {\n                    authError: authError?.message\n                }\n            }, {\n                status: 401\n            });\n        }\n        // Fetch remittance transactions for the current user\n        const { data: remittances, error: fetchError } = await supabase.from('remittance_transactions').select('*').eq('sender_user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        if (fetchError) {\n            console.error('Error fetching remittance history:', fetchError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Failed to fetch remittance history',\n                details: {\n                    fetchError: fetchError.message\n                }\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'success',\n            data: remittances || []\n        });\n    } catch (error) {\n        console.error('Unexpected error in remittance history API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            message: 'An unexpected error occurred',\n            details: {\n                error: error.message\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/quikr/remittances/history/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute&page=%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute.ts&appDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute&page=%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute.ts&appDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Sklli_Desktop_quikrsolutions_platform_quikrsolutions_app_app_api_quikr_remittances_history_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/quikr/remittances/history/route.ts */ \"(rsc)/./app/api/quikr/remittances/history/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/quikr/remittances/history/route\",\n        pathname: \"/api/quikr/remittances/history\",\n        filename: \"route\",\n        bundlePath: \"app/api/quikr/remittances/history/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\quikrsolutions_platform\\\\quikrsolutions-app\\\\app\\\\api\\\\quikr\\\\remittances\\\\history\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Sklli_Desktop_quikrsolutions_platform_quikrsolutions_app_app_api_quikr_remittances_history_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute&page=%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute.ts&appDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* reexport safe */ _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient),\n/* harmony export */   createSupabaseAdminClient: () => (/* binding */ createSupabaseAdminClient),\n/* harmony export */   createSupabaseServerClient: () => (/* binding */ createSupabaseServerClient),\n/* harmony export */   createSupabaseServerClientWithCookies: () => (/* binding */ createSupabaseServerClientWithCookies)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/lib/supabase/server.ts\n\n\n // Changed from require\n // Export createServerClient\n// For Server Components, Route Handlers\nasync function createSupabaseServerClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://ivjlbjxbnmxgbwsiryys.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml2amxianhibm14Z2J3c2lyeXlzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4MjM3ODUsImV4cCI6MjA2MzM5OTc4NX0.HTi6esUoEI86zk-Tdd8EdX5SRVxA9UcKmoTKpJyy78c\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                cookieStore.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                cookieStore.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n}\n// For Route Handlers and Server Actions where you pass the cookie store explicitly,\n// for example, in a Server Action where `cookies()` might not be directly available\n// or when working with specific cookie manipulation logic.\n// Reverting to a simpler type for cookieStore and using set for remove.\nasync function createSupabaseServerClientWithCookies(cookieStore) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://ivjlbjxbnmxgbwsiryys.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml2amxianhibm14Z2J3c2lyeXlzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4MjM3ODUsImV4cCI6MjA2MzM5OTc4NX0.HTi6esUoEI86zk-Tdd8EdX5SRVxA9UcKmoTKpJyy78c\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                cookieStore.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                cookieStore.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n}\n// For Admin-level operations\nfunction createSupabaseAdminClient() {\n    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined. This client is for admin operations only.');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://ivjlbjxbnmxgbwsiryys.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            persistSession: false,\n            autoRefreshToken: false\n        },\n        global: {\n            headers: {\n                'x-supabase-api-key': process.env.SUPABASE_SERVICE_ROLE_KEY\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute&page=%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquikr%2Fremittances%2Fhistory%2Froute.ts&appDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSklli%5CDesktop%5Cquikrsolutions_platform%5Cquikrsolutions-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();