import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, Phone, Globe, Star } from "lucide-react"

export default function PlacesPage() {
  const places = [
    {
      id: 1,
      name: "Tech Solutions Inc",
      category: "Technology",
      description: "Leading software development company",
      address: "123 Tech Street, Digital City",
      phone: "+****************",
      website: "techsolutions.com",
      rating: 4.8,
      reviews: 124
    },
    {
      id: 2,
      name: "Green Garden Cafe",
      category: "Restaurant",
      description: "Organic, locally-sourced dining experience",
      address: "456 Garden Ave, Green Valley",
      phone: "+****************",
      website: "greengarden.com",
      rating: 4.6,
      reviews: 89
    },
    {
      id: 3,
      name: "Fitness First Gym",
      category: "Health & Fitness",
      description: "State-of-the-art fitness facility",
      address: "789 Fitness Blvd, Healthy Heights",
      phone: "+****************",
      website: "fitnessfirst.com",
      rating: 4.7,
      reviews: 156
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Places Directory</h1>
          <p className="text-muted-foreground">Discover and connect with local businesses</p>
        </div>
        <Button>Add Your Business</Button>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <Input 
          placeholder="Search places..." 
          className="max-w-md"
        />
        <Button variant="outline">Filter by Category</Button>
        <Button variant="outline">Sort by Rating</Button>
      </div>

      {/* Places Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {places.map((place) => (
          <Card key={place.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{place.name}</CardTitle>
                  <Badge variant="secondary" className="mt-1">{place.category}</Badge>
                </div>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{place.rating}</span>
                  <span className="text-xs text-muted-foreground">({place.reviews})</span>
                </div>
              </div>
              <CardDescription>{place.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{place.address}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>{place.phone}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <a href={`https://${place.website}`} className="text-primary hover:underline">
                  {place.website}
                </a>
              </div>
              <div className="flex gap-2 pt-2">
                <Button size="sm" className="flex-1">Contact</Button>
                <Button size="sm" variant="outline" className="flex-1">View Details</Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      <div className="text-center">
        <Button variant="outline">Load More Places</Button>
      </div>
    </div>
  )
}
