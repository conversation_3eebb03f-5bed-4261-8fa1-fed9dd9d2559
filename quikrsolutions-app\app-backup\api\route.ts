// src/app/api/auth/callback/route.ts
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createSupabaseServerClientWithCookies } from '@/lib/supabase/server'; // Use the version that takes cookies()

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  // if "next" is in param, use it as the redirect URL
  const next = searchParams.get('next') ?? '/dashboard'; // Default to dashboard

  if (code) {
    const cookieStore = cookies();
    const supabase = await createSupabaseServerClientWithCookies(cookieStore); // Pass cookieStore
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    if (!error) {
      return NextResponse.redirect(`${origin}${next}`);
    }
  }

  // return the user to an error page with instructions
  console.error('OAuth callback error: No code or exchange failed.');
  return NextResponse.redirect(`${origin}/login?error=OAuthCallbackFailed`);
}
