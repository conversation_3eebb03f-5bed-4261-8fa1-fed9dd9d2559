'use server';

import { supabaseAdmin } from '@/lib/supabase/admin';
import { revalidatePath } from 'next/cache';

export async function updateUserRole(userId: string, newRole: string) {
  try {
    let app_metadata: { role?: string | null; is_admin?: boolean | null };

    if (newRole === 'super_admin_special') {
      // Set is_admin to true and clear the 'role' field
      app_metadata = { is_admin: true, role: null };
    } else {
      // Set the 'role' field and ensure is_admin is false or null
      app_metadata = { role: newRole, is_admin: false };
    }

    const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, {
      app_metadata: app_metadata,
    });

    if (error) {
      console.error('Error updating user role:', error);
      return { success: false, error: error.message };
    }

    revalidatePath('/admin');
    return { success: true };

  } catch (error: any) {
    console.error('Unexpected error updating user role:', error);
    return { success: false, error: error.message };
  }
}
