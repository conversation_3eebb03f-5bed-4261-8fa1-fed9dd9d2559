#!/usr/bin/env node

/**
 * Webhook Simulation Script for Quikrsolutions Remittance Testing
 * 
 * Usage:
 *   node simulate_webhook.js <status> [remittance_id]
 * 
 * Examples:
 *   node simulate_webhook.js processing qr_rem_987654321
 *   node simulate_webhook.js completed qr_rem_987654321
 *   node simulate_webhook.js failed qr_rem_987654321
 * 
 * Available statuses: processing, in_transit, ready_for_pickup, completed, failed
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Configuration
const WEBHOOK_URL = 'http://localhost:3000/api/webhooks/eloh/remittance';
const WEBHOOK_SECRET = 'your-webhook-secret-key'; // Should match your .env.local

// Load test payloads
const payloadsPath = path.join(__dirname, 'webhook-test-payloads.json');
const testPayloads = JSON.parse(fs.readFileSync(payloadsPath, 'utf8'));

// Get command line arguments
const [,, status, remittanceId] = process.argv;

if (!status) {
  console.error('Usage: node simulate_webhook.js <status> [remittance_id]');
  console.error('Available statuses:', Object.keys(testPayloads).map(k => k.replace('_status', '')).join(', '));
  process.exit(1);
}

const statusKey = `${status}_status`;
if (!testPayloads[statusKey]) {
  console.error(`Invalid status: ${status}`);
  console.error('Available statuses:', Object.keys(testPayloads).map(k => k.replace('_status', '')).join(', '));
  process.exit(1);
}

// Get the payload template
let payload = JSON.parse(JSON.stringify(testPayloads[statusKey].payload));

// Update remittance ID if provided
if (remittanceId) {
  payload.data.quikrsolutions_remittance_id = remittanceId;
}

// Update timestamps to current time
const now = new Date().toISOString();
payload.timestamp = now;
payload.data.updated_at = now;

// Generate signature
function generateSignature(payload, secret) {
  const payloadString = JSON.stringify(payload);
  return crypto
    .createHmac('sha256', secret)
    .update(payloadString)
    .digest('hex');
}

const signature = generateSignature(payload, WEBHOOK_SECRET);

// Send webhook
async function sendWebhook() {
  try {
    console.log('Sending webhook...');
    console.log('Status:', status);
    console.log('Remittance ID:', payload.data.quikrsolutions_remittance_id);
    console.log('Payload:', JSON.stringify(payload, null, 2));
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Eloh-Signature': `sha256=${signature}`,
        'User-Agent': 'Eloh-Webhook-Simulator/1.0'
      },
      body: JSON.stringify(payload)
    });

    console.log('\nResponse Status:', response.status);
    console.log('Response Headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Response Body:', responseText);

    if (response.ok) {
      console.log('\n✅ Webhook sent successfully!');
    } else {
      console.log('\n❌ Webhook failed!');
    }

  } catch (error) {
    console.error('\n❌ Error sending webhook:', error.message);
  }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.error('This script requires Node.js 18+ or you need to install node-fetch');
  console.error('Install with: npm install node-fetch');
  process.exit(1);
}

sendWebhook();
