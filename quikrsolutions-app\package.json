{"name": "quikrsolutions-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.7", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "geist": "^1.4.2", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "sonner": "^2.0.3", "stripe": "^18.1.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.27.0", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.27.0", "eslint-config-next": "15.3.2", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5", "typescript-eslint": "^8.32.1"}}