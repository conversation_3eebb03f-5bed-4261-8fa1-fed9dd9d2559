'use client';

import { useEffect, useState } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client'; // Use the client-side Supabase client
import { Database } from '@/types/supabase'; // Replace with your actual types
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils"; // Assuming cn utility is available
import { Loader2 } from "lucide-react"; // Assuming lucide-react is installed

interface RealtimeTransactionProps {
  orderId: string;
  userId: string;
}

const RealtimeTransaction: React.FC<RealtimeTransactionProps> = ({ orderId, userId }) => {
  const [transactionStatus, setTransactionStatus] = useState<string | null>(null);
  const supabase = createSupabaseBrowserClient();

  useEffect(() => {
    // Only subscribe if orderId is provided
    if (!orderId) {
      return;
    }

    const channel = supabase
      .channel(`eloh_crypto_transactions_${orderId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'eloh_crypto_transactions',
          filter: `quikrsolutions_order_id=eq.${orderId}`,
        },
        (payload: { new: Database['public']['Tables']['eloh_crypto_transactions']['Row'] }) => {
          console.log('Received Realtime update:', payload);
          const newStatus = payload.new.status;
          setTransactionStatus(newStatus);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [orderId, supabase]);

  const statusBadge = () => {
    switch (transactionStatus) {
      case "pending_payment":
        return <Badge variant="secondary">Pending Payment</Badge>;
      case "processing":
        return (
          <Badge variant="outline">
            Processing <Loader2 className="ml-2 h-4 w-4 animate-spin" />
          </Badge>
        );
      case "confirmed":
        return <Badge variant="default" className="bg-green-500 hover:bg-green-500">Confirmed</Badge>; // Using default and adding green background
      case "invoice_failed_or_expired":
        return <Badge variant="destructive">Failed/Expired</Badge>;
      default:
        return <Badge>{transactionStatus || 'Unknown Status'}</Badge>;
    }
  };

  return (
    <div>
      <h2 className="text-lg font-semibold mb-2">Transaction Details</h2>
      <p className="mb-1">Order ID: {orderId}</p>
      <div className="flex items-center mb-4">
        <p className="mr-2">Status:</p>
        {statusBadge()}
      </div>
      {/* You might want to fetch and display more transaction details here if needed */}
      {/* For now, we focus on the status update */}
    </div>
  );
};

export default RealtimeTransaction;
