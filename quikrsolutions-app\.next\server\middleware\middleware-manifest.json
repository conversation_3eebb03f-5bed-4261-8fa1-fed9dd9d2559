{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1531e49d._.js", "server/edge/chunks/[root-of-the-server]__004c5692._.js", "server/edge/chunks/edge-wrapper_733c3edd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z9PPNSj/YWFrw5ioAWDsPog7dEpHBvFszhaZqUDq7zI=", "__NEXT_PREVIEW_MODE_ID": "3d71505d23f04704cd1ce1839c4e696c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "605fb3e82e2eac344d15865755cfba357499f909f216d6314f93835b44eda495", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a4a69a3d9fe2452b79b4a212468e25327d334a4fd1b77dbf7a456bdd260320d2"}}}, "instrumentation": null, "functions": {}}