{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1531e49d._.js", "server/edge/chunks/[root-of-the-server]__004c5692._.js", "server/edge/chunks/edge-wrapper_733c3edd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z9PPNSj/YWFrw5ioAWDsPog7dEpHBvFszhaZqUDq7zI=", "__NEXT_PREVIEW_MODE_ID": "10f2aa168425080ef7fbc78621e3e176", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3623c073f6c763ba13d15e1dcb39ad50f5780f01834b5277a71bb2e6195d0e25", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0b68bf882d81d4f13bc5b04f29a7bb927e7e70877320d396fe986d12b6509d66"}}}, "instrumentation": null, "functions": {}}