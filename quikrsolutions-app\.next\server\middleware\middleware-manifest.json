{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1531e49d._.js", "server/edge/chunks/[root-of-the-server]__004c5692._.js", "server/edge/chunks/edge-wrapper_733c3edd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z9PPNSj/YWFrw5ioAWDsPog7dEpHBvFszhaZqUDq7zI=", "__NEXT_PREVIEW_MODE_ID": "9bd4de19d7993f7203d581bbff0322a0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "da29ab2a636cf9212b2631b437d2cb5d54f706b4c06fbec28cbc2e9f5a754a66", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3615914aa340c78b5c0c74795f97807199890e838f074714be2ce7afefa78496"}}}, "instrumentation": null, "functions": {}}