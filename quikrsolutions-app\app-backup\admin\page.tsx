// quikrsolutions-app/src/app/admin/page.tsx
import React from 'react';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { supabaseAdmin } from '@/lib/supabase/admin'; // Import the exported admin client instance
import { redirect } from 'next/navigation';
import { User } from '@supabase/supabase-js'; // Import the User type
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"; // Import shadcn/ui table components
import { UserRoleSelect } from '@/components/admin/UserRoleSelect'; // Import the UserRoleSelect component

export default async function AdminDashboardPage() {
  const supabase = await createSupabaseServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  // Check if user is logged in and is an admin
  if (!user || user.app_metadata.is_admin !== true) {
    // Redirect non-admin users (or logged out users) to the dashboard or home page
    redirect('/dashboard'); // Or redirect to '/' or '/login'
  }

  // Fetch users using the admin client
  const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers(); // Access listUsers via auth.admin

  if (error) {
    console.error('Error fetching users:', error);
    // Handle error, maybe display a message or redirect
    return (
      <div className="flex flex-col items-center justify-center min-h-screen py-2">
        <h1 className="text-4xl font-bold">Admin Dashboard</h1>
        <p className="mt-3 text-xl text-red-500">Error loading users.</p>
      </div>
    );
  }

  // Define available roles for the select dropdown
  const availableRoles = ['admin', 'driver', 'food_vendor', 'product_vendor', 'service_vendor', 'authenticated'];

  // Admin dashboard content goes here
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-4xl font-bold mb-6">Admin Dashboard</h1>
      <p className="mt-3 text-xl mb-8">Welcome, Administrator {user.email}</p>

      <h2 className="text-2xl font-semibold mb-4">User Management</h2>

      <Table>
        <TableCaption>A list of users in your Supabase project.</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Last Sign In At</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Actions</TableHead> {/* Add Actions header */}
            {/* Add more headers as needed */}
          </TableRow>
        </TableHeader>
        <TableBody>{users.map((u: User) => ( // Use the imported User type
            <TableRow key={u.id}><TableCell className="font-medium">{u.id}</TableCell><TableCell>{u.email}</TableCell><TableCell>{new Date(u.created_at).toLocaleDateString()}</TableCell><TableCell>{u.last_sign_in_at ? new Date(u.last_sign_in_at).toLocaleDateString() : 'N/A'}</TableCell><TableCell>{u.app_metadata?.is_admin === true ? 'Admin' : u.app_metadata?.role || u.role}</TableCell> {/* Display custom role or Admin status */}<TableCell>{/* Use the UserRoleSelect component */}<UserRoleSelect user={u} availableRoles={availableRoles} /></TableCell> {/* Add Actions cell */}{/* Add more cells as needed */}</TableRow>))}
        </TableBody>
      </Table>

      {/* Add sections for analytics, products, services later */}
    </div>
  );
}
