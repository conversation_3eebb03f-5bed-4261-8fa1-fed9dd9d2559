// src/app/api/nowpayments/webhook/route.ts
import crypto from 'crypto';
import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function POST(request: Request) {
  try {
    const supabase = await createSupabaseServerClient();
    const headersList = await headers();
    const nowpaymentsSignature = headersList.get('x-nowpayments-sig');

    if (!nowpaymentsSignature) {
      return NextResponse.json({ error: 'No signature sent' }, { status: 400 });
    }

    const body = await request.json();
    const ipnSecret = process.env.NOWPAYMENTS_IPN_SECRET || '';

    const data = JSON.stringify(body); // Use all the body for signature verification

    const expectedSignature = crypto
      .createHmac('sha512', ipnSecret)
      .update(data)
      .digest('hex');

    if (nowpaymentsSignature !== expectedSignature) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    const { payment_id, payment_status } = body;

    // Update the payments table in Supabase
    const { error } = await supabase
      .from('payments')
      .update({ status: payment_status })
      .eq('id', payment_id);

    if (error) {
      console.error('Error updating payments table:', error);
      return NextResponse.json({ error: 'Database update failed' }, { status: 500 });
    }

    console.log('NowPayments webhook received:', body);

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
