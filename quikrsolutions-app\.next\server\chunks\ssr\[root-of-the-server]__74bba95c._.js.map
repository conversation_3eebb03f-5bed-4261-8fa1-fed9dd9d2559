{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/lib/supabase/server.ts"], "sourcesContent": ["// src/lib/supabase/server.ts\r\nimport { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { cookies } from 'next/headers';\r\nimport { createClient } from '@supabase/supabase-js'; // Changed from require\r\n\r\nexport { createServerClient } from '@supabase/ssr'; // Export createServerClient\r\n\r\n// For Server Components, Route Handlers\r\nexport async function createSupabaseServerClient() {\r\n  const cookieStore = await cookies();\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          cookieStore.set({ name, value, ...options });\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          cookieStore.set({ name, value: '', ...options });\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// For Route Handlers and Server Actions where you pass the cookie store explicitly,\r\n// for example, in a Server Action where `cookies()` might not be directly available\r\n// or when working with specific cookie manipulation logic.\r\n// Reverting to a simpler type for cookieStore and using set for remove.\r\nexport async function createSupabaseServerClientWithCookies(cookieStore: any) { // Changed any to any as per linter error\r\n    return createServerClient(\r\n        process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n        {\r\n            cookies: {\r\n                get(name: string) {\r\n                return cookieStore.get(name)?.value\r\n                },\r\n                set(name: string, value: string, options: CookieOptions) {\r\n                cookieStore.set({ name, value, ...options })\r\n                },\r\n                remove(name: string, options: CookieOptions) {\r\n                cookieStore.set({ name, value: '', ...options })\r\n                },\r\n            },\r\n        }\r\n    )\r\n}\r\n\r\n// For Admin-level operations\r\nexport function createSupabaseAdminClient() {\r\n  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {\r\n    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined. This client is for admin operations only.');\r\n  }\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        persistSession: false,\r\n        autoRefreshToken: false,\r\n      },\r\n      global: {\r\n        headers: { 'x-supabase-api-key': process.env.SUPABASE_SERVICE_ROLE_KEY },\r\n      },\r\n    }\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;AAC7B;AAAA;AACA;AACA,4TAAsD,uBAAuB;;;;;AAKtE,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAsB;gBACrD,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC5C;YACA,QAAO,IAAY,EAAE,OAAsB;gBACzC,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAChD;QACF;IACF;AAEJ;AAMO,eAAe,sCAAsC,WAAgB;IACxE,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGpB;QACI,SAAS;YACL,KAAI,IAAY;gBAChB,OAAO,YAAY,GAAG,CAAC,OAAO;YAC9B;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAsB;gBACvD,YAAY,GAAG,CAAC;oBAAE;oBAAM;oBAAO,GAAG,OAAO;gBAAC;YAC1C;YACA,QAAO,IAAY,EAAE,OAAsB;gBAC3C,YAAY,GAAG,CAAC;oBAAE;oBAAM,OAAO;oBAAI,GAAG,OAAO;gBAAC;YAC9C;QACJ;IACJ;AAER;AAGO,SAAS;IACd,IAAI,CAAC,QAAQ,GAAG,CAAC,yBAAyB,EAAE;QAC1C,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,gFAEhB,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,MAAM;YACJ,gBAAgB;YAChB,kBAAkB;QACpB;QACA,QAAQ;YACN,SAAS;gBAAE,sBAAsB,QAAQ,GAAG,CAAC,yBAAyB;YAAC;QACzE;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/lib/supabase/admin.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\r\n\r\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\r\n\r\nif (!supabaseUrl) throw new Error(\"Missing env.NEXT_PUBLIC_SUPABASE_URL\");\r\nif (!supabaseServiceRoleKey) throw new Error(\"Missing env.SUPABASE_SERVICE_ROLE_KEY\");\r\n\r\nexport const supabaseAdmin = createClient(\r\n  supabaseUrl,\r\n  supabaseServiceRoleKey,\r\n  { auth: { autoRefreshToken: false, persistSession: false } }\r\n);"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;AACN,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB;AAEpE,uCAAkB;;AAAuD;AACzE,IAAI,CAAC,wBAAwB,MAAM,IAAI,MAAM;AAEtC,MAAM,gBAAgB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACtC,aACA,wBACA;IAAE,MAAM;QAAE,kBAAkB;QAAO,gBAAgB;IAAM;AAAE", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"Table\",\n);\nexport const TableBody = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableBody\",\n);\nexport const TableCaption = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableCaption\",\n);\nexport const TableCell = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableCell\",\n);\nexport const TableFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableFooter\",\n);\nexport const TableHead = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableHead\",\n);\nexport const TableHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableHeader\",\n);\nexport const TableRow = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx <module evaluation>\",\n    \"TableRow\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,6DACA", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/table.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"Table\",\n);\nexport const TableBody = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableBody\",\n);\nexport const TableCaption = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableCaption\",\n);\nexport const TableCell = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableCell\",\n);\nexport const TableFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableFooter\",\n);\nexport const TableHead = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableHead\",\n);\nexport const TableHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableHeader\",\n);\nexport const TableRow = registerClientReference(\n    function() { throw new Error(\"Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/table.tsx\",\n    \"TableRow\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yCACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/admin/UserRoleSelect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserRoleSelect = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserRoleSelect() from the server but UserRoleSelect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin/UserRoleSelect.tsx <module evaluation>\",\n    \"UserRoleSelect\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yEACA", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/admin/UserRoleSelect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserRoleSelect = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserRoleSelect() from the server but UserRoleSelect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin/UserRoleSelect.tsx\",\n    \"UserRoleSelect\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qDACA", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/app/admin/page.tsx"], "sourcesContent": ["// quikrsolutions-app/src/app/admin/page.tsx\r\nimport React from 'react';\r\nimport { createSupabaseServerClient } from '@/lib/supabase/server';\r\nimport { supabaseAdmin } from '@/lib/supabase/admin'; // Import the exported admin client instance\r\nimport { redirect } from 'next/navigation';\r\nimport { User } from '@supabase/supabase-js'; // Import the User type\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\"; // Import shadcn/ui table components\r\nimport { UserRoleSelect } from '@/components/admin/UserRoleSelect'; // Import the UserRoleSelect component\r\n\r\nexport default async function AdminDashboardPage() {\r\n  const supabase = await createSupabaseServerClient();\r\n  const { data: { user } } = await supabase.auth.getUser();\r\n\r\n  // Check if user is logged in and is an admin\r\n  if (!user || user.app_metadata.is_admin !== true) {\r\n    // Redirect non-admin users (or logged out users) to the dashboard or home page\r\n    redirect('/dashboard'); // Or redirect to '/' or '/login'\r\n  }\r\n\r\n  // Fetch users using the admin client\r\n  const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers(); // Access listUsers via auth.admin\r\n\r\n  if (error) {\r\n    console.error('Error fetching users:', error);\r\n    // Handle error, maybe display a message or redirect\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-screen py-2\">\r\n        <h1 className=\"text-4xl font-bold\">Admin Dashboard</h1>\r\n        <p className=\"mt-3 text-xl text-red-500\">Error loading users.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Define available roles for the select dropdown\r\n  const availableRoles = ['admin', 'driver', 'food_vendor', 'product_vendor', 'service_vendor', 'authenticated'];\r\n\r\n  // Admin dashboard content goes here\r\n  return (\r\n    <div className=\"container mx-auto py-10\">\r\n      <h1 className=\"text-4xl font-bold mb-6\">Admin Dashboard</h1>\r\n      <p className=\"mt-3 text-xl mb-8\">Welcome, Administrator {user.email}</p>\r\n\r\n      <h2 className=\"text-2xl font-semibold mb-4\">User Management</h2>\r\n\r\n      <Table>\r\n        <TableCaption>A list of users in your Supabase project.</TableCaption>\r\n        <TableHeader>\r\n          <TableRow>\r\n            <TableHead>ID</TableHead>\r\n            <TableHead>Email</TableHead>\r\n            <TableHead>Created At</TableHead>\r\n            <TableHead>Last Sign In At</TableHead>\r\n            <TableHead>Role</TableHead>\r\n            <TableHead>Actions</TableHead> {/* Add Actions header */}\r\n            {/* Add more headers as needed */}\r\n          </TableRow>\r\n        </TableHeader>\r\n        <TableBody>{users.map((u: User) => ( // Use the imported User type\r\n            <TableRow key={u.id}><TableCell className=\"font-medium\">{u.id}</TableCell><TableCell>{u.email}</TableCell><TableCell>{new Date(u.created_at).toLocaleDateString()}</TableCell><TableCell>{u.last_sign_in_at ? new Date(u.last_sign_in_at).toLocaleDateString() : 'N/A'}</TableCell><TableCell>{u.app_metadata?.is_admin === true ? 'Admin' : u.app_metadata?.role || u.role}</TableCell> {/* Display custom role or Admin status */}<TableCell>{/* Use the UserRoleSelect component */}<UserRoleSelect user={u} availableRoles={availableRoles} /></TableCell> {/* Add Actions cell */}{/* Add more cells as needed */}</TableRow>))}\r\n        </TableBody>\r\n      </Table>\r\n\r\n      {/* Add sections for analytics, products, services later */}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;;AAE5C;AAAA;AACA,6NAAsD,4CAA4C;AAClG;AAAA;AAEA,iOAQgC,oCAAoC;AACpE,yPAAoE,sCAAsC;;;;;;;AAE3F,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,gJAAA,CAAA,6BAA0B,AAAD;IAChD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAEtD,6CAA6C;IAC7C,IAAI,CAAC,QAAQ,KAAK,YAAY,CAAC,QAAQ,KAAK,MAAM;QAChD,+EAA+E;QAC/E,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,iCAAiC;IAC3D;IAEA,qCAAqC;IACrC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,+HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,kCAAkC;IAEjH,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,yBAAyB;QACvC,oDAAoD;QACpD,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAqB;;;;;;8BACnC,8OAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;IAG/C;IAEA,iDAAiD;IACjD,MAAM,iBAAiB;QAAC;QAAS;QAAU;QAAe;QAAkB;QAAkB;KAAgB;IAE9G,oCAAoC;IACpC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAE,WAAU;;oBAAoB;oBAAwB,KAAK,KAAK;;;;;;;0BAEnE,8OAAC;gBAAG,WAAU;0BAA8B;;;;;;0BAE5C,8OAAC,iIAAA,CAAA,QAAK;;kCACJ,8OAAC,iIAAA,CAAA,eAAY;kCAAC;;;;;;kCACd,8OAAC,iIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;8CACP,8OAAC,iIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,iIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,iIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,iIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,iIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,iIAAA,CAAA,YAAS;8CAAC;;;;;;gCAAmB;;;;;;;;;;;;kCAIlC,8OAAC,iIAAA,CAAA,YAAS;kCAAE,MAAM,GAAG,CAAC,CAAC,kBACnB,8OAAC,iIAAA,CAAA,WAAQ;;kDAAY,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAe,EAAE,EAAE;;;;;;kDAAa,8OAAC,iIAAA,CAAA,YAAS;kDAAE,EAAE,KAAK;;;;;;kDAAa,8OAAC,iIAAA,CAAA,YAAS;kDAAE,IAAI,KAAK,EAAE,UAAU,EAAE,kBAAkB;;;;;;kDAAe,8OAAC,iIAAA,CAAA,YAAS;kDAAE,EAAE,eAAe,GAAG,IAAI,KAAK,EAAE,eAAe,EAAE,kBAAkB,KAAK;;;;;;kDAAkB,8OAAC,iIAAA,CAAA,YAAS;kDAAE,EAAE,YAAY,EAAE,aAAa,OAAO,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI;;;;;;oCAAa;kDAA4C,8OAAC,iIAAA,CAAA,YAAS;kDAAyC,cAAA,8OAAC,6IAAA,CAAA,iBAAc;4CAAC,MAAM;4CAAG,gBAAgB;;;;;;;;;;;oCAA8B;;+BAA/gB,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;AAO/B", "debugId": null}}]}