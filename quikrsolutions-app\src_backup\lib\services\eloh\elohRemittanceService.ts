// src/lib/services/eloh/elohRemittanceService.ts

import {
  GetElohExchangeRateParams,
  GetElohExchangeRateResult,
  InitiateElohRemittanceParams,
  InitiateElohRemittanceResult,
  GetElohRemittanceStatusParams,
  GetElohRemittanceStatusResult,
  ElohErrorResponse,
} from './elohTypes';

const MOCK_ELOH_API_BASE_URL = 'http://localhost:3000/api/mock-eloh/v1'; // Or process.env.ELOH_API_BASE_URL for real Eloh

export async function initiateRemittance(
  params: InitiateElohRemittanceParams
): Promise<InitiateElohRemittanceResult> {
  try {
    const response = await fetch(
      `/api/eloh/v1/remittances/stellar`, // Existing Quikrsolutions API route
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      }
    );

    const data = await response.json();

    if (response.ok) {
      // Assuming the Quikrsolutions API route returns data that matches ElohRemittanceSuccessResponse['data']
      return { status: 'success', data: data };
    } else {
      return data as ElohErrorResponse;
    }
  } catch (error: any) {
    console.error('Error initiating remittance:', error);
    return {
      status: 'error',
      message: 'Failed to initiate remittance',
      details: {
        error: error.message,
      },
    };
  }
}

export async function getExchangeRate(
  params: GetElohExchangeRateParams
): Promise<GetElohExchangeRateResult> {
  try {
    // Construct query string from params
    const queryString = new URLSearchParams({
      fromCurrency: params.fromCurrency,
      toCurrency: params.toCurrency,
      amount: params.amount.toString(),
    }).toString();

    const response = await fetch(
      `${MOCK_ELOH_API_BASE_URL}/remittances/exchange-rates?${queryString}`, // Corrected path to mock endpoint
      {
        method: 'GET', // Change method to GET
        // No need for 'Content-Type': 'application/json' or body for GET request
      }
    );

    // Check if the response is OK (status code 200-299)
    if (!response.ok) {
       const errorBody = await response.text(); // Read response body as text for debugging
       console.error(`Mock exchange rate endpoint returned status ${response.status}: ${errorBody}`);
       return {
         status: 'error',
         message: `Mock exchange rate endpoint returned status ${response.status}`,
         details: {
           statusCode: response.status,
           body: errorBody,
         },
       };
    }

    const data = await response.json();

    // Assuming the mock API returns data that matches ElohExchangeRateSuccessResponse['data']
    return { status: 'success', data: data };

  } catch (error: any) {
    console.error('Error getting exchange rate:', error);
    return {
      status: 'error',
      message: 'Failed to get exchange rate',
      details: {
        error: error.message,
      },
    };
  }
}

export async function getRemittanceStatus(
  params: GetElohRemittanceStatusParams
): Promise<GetElohRemittanceStatusResult> {
  try {
    const response = await fetch(
      `${MOCK_ELOH_API_BASE_URL}/remittance-status`, // New mock endpoint
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      }
    );

    const data = await response.json();

    if (response.ok) {
      // Assuming the mock API returns data that matches RemittanceStatusResponse['data']
      return { status: 'success', data: data };
    } else {
      return data as ElohErrorResponse;
    }
  } catch (error: any) {
    console.error('Error getting remittance status:', error);
    return {
      status: 'error',
      message: 'Failed to get remittance status',
      details: {
        error: error.message,
      },
    };
  }
}
