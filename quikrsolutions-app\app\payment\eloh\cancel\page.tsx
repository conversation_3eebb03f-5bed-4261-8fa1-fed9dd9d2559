import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from 'next/link';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";


const CancelPage = () => {
  return (
    <div className="container mx-auto mt-8 px-4">
      <Card>
        <CardHeader>
          <CardTitle>Payment Cancelled</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="default">
            <Terminal className="h-4 w-4" />
            <AlertTitle>Payment Cancelled</AlertTitle>
            <AlertDescription>
              Your crypto payment was cancelled or did not complete.
            </AlertDescription>
          </Alert>
          <p className="mt-4 mb-4">You can try the payment again or contact support if you encountered an issue.</p>
          <div className="flex space-x-4">
            <Link href="/subscriptions" passHref>
              <Button>Try Payment Again</Button>
            </Link>
            {/* Add a link/button for Contact Support if available */}
            {/* <Button variant="outline">Contact Support</Button> */}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CancelPage;
