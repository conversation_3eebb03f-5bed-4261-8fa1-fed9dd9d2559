{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/app/%28auth%29/layout.tsx"], "sourcesContent": ["// src/app/(auth)/layout.tsx\r\nimport React from 'react';\r\n\r\nexport default function AuthLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return (\r\n    <main className=\"flex min-h-screen flex-col items-center justify-center bg-gradient-to-br from-slate-900 to-slate-800 p-4\">\r\n      {/* You could add a logo or app name here using GradientText later */}\r\n      {children}\r\n    </main>\r\n  );\r\n}"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;AAGb,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,WAAU;kBAEb;;;;;;AAGP", "debugId": null}}]}