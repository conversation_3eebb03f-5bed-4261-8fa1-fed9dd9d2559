export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      eloh_crypto_transactions: {
        Row: {
          amount_fiat: number | null
          amount_paid_crypto: string | null
          btcpay_invoice_id: string | null
          created_at: string
          crypto_currency: string | null
          eloh_transaction_id: string | null
          fiat_currency: string | null
          id: string
          paid_at: string | null
          quikrsolutions_order_id: string | null
          status: string | null
          transaction_hash: string | null
          user_id: string | null
        }
        Insert: {
          amount_fiat?: number | null
          amount_paid_crypto?: string | null
          btcpay_invoice_id?: string | null
          created_at?: string
          crypto_currency?: string | null
          eloh_transaction_id?: string | null
          fiat_currency?: string | null
          id?: string
          paid_at?: string | null
          quikrsolutions_order_id?: string | null
          status?: string | null
          transaction_hash?: string | null
          user_id?: string | null
        }
        Update: {
          amount_fiat?: number | null
          amount_paid_crypto?: string | null
          btcpay_invoice_id?: string | null
          created_at?: string
          crypto_currency?: string | null
          eloh_transaction_id?: string | null
          fiat_currency?: string | null
          id?: string
          paid_at?: string | null
          quikrsolutions_order_id?: string | null
          status?: string | null
          transaction_hash?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "eloh_crypto_transactions_user_id_fkey"
            columns: ["user_id"]
            referencedBy: []
            references: {
              id: "users"
              table: "users"
            }
          }
        ]
      }
      remittance_transactions: {
        Row: {
          created_at: string
          eloh_remittance_id: string | null
          exchange_rate: number
          fees: number
          id: string
          quikrsolutions_remittance_id: string
          receive_amount: number
          receive_currency: string
          recipient_contact: string
          recipient_country: string
          recipient_name: string
          send_amount: number
          send_currency: string
          status: string
          stellar_transaction_hash: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          eloh_remittance_id?: string | null
          exchange_rate: number
          fees: number
          id?: string
          quikrsolutions_remittance_id: string
          receive_amount: number
          receive_currency: string
          recipient_contact: string
          recipient_country: string
          recipient_name: string
          send_amount: number
          send_currency: string
          status: string
          stellar_transaction_hash?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          eloh_remittance_id?: string | null
          exchange_rate?: number
          fees?: number
          id?: string
          quikrsolutions_remittance_id?: string
          receive_amount?: number
          receive_currency?: string
          recipient_contact?: string
          recipient_country?: string
          recipient_name?: string
          send_amount?: number
          send_currency?: string
          status?: string
          stellar_transaction_hash?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "remittance_transactions_user_id_fkey"
            columns: ["user_id"]
            referencedBy: []
            references: {
              id: "users"
              table: "users"
            }
          }
        ]
      }
      profiles: {
        Row: {
          id: string
          user_id: string | null
          // Add other profile columns as needed
        }
        Insert: {
          id?: string
          user_id?: string | null
          // Add other profile columns as needed
        }
        Update: {
          id?: string
          user_id?: string | null
          // Add other profile columns as needed
        }
        Relationships: [
          {
            foreignKeyName: "profiles_user_id_fkey"
            columns: ["user_id"]
            referencedBy: []
            references: {
              id: "users"
              table: "users"
            }
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
