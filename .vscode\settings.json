{"deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "[typescript]": {"editor.defaultFormatter": "denoland.vscode-deno"}, "css.validate": false, "less.validate": false, "scss.validate": false, "css.customData": [".vscode/tailwind.json"], "tailwindCSS.experimental.configFile": null, "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "tailwindCSS.classAttributes": ["class", "className", "ngClass"]}