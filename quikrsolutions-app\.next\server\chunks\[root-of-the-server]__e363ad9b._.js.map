{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/app/api/test/route.ts"], "sourcesContent": ["export async function GET() {\n  console.log('Test API route called');\n  return new Response(JSON.stringify({\n    status: 'success',\n    message: 'Test API route is working',\n    timestamp: new Date().toISOString()\n  }), {\n    headers: { 'Content-Type': 'application/json' }\n  });\n}\n"], "names": [], "mappings": ";;;AAAO,eAAe;IACpB,QAAQ,GAAG,CAAC;IACZ,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;QACjC,QAAQ;QACR,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;IACnC,IAAI;QACF,SAAS;YAAE,gBAAgB;QAAmB;IAChD;AACF", "debugId": null}}]}