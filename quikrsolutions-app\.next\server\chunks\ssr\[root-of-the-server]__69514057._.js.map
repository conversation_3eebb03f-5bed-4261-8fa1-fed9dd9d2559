{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/components/ui/use-toast.ts"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { ToastProps } from \"@/components/ui/toast\" // Assuming ToastProps is exported from toast.tsx\r\n\r\nconst TOAST_LIMIT = 1\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: React.ReactNode\r\n}\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n} as const\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString()\r\n}\r\n\r\ntype ActionType = typeof actionTypes\r\n\r\ntype Action =\r\n  | {\r\n      type: ActionType[\"ADD_TOAST\"]\r\n      toast: ToasterToast\r\n    }\r\n  | {\r\n      type: ActionType[\"UPDATE_TOAST\"]\r\n      toast: Partial<ToasterToast>\r\n    }\r\n  | {\r\n      type: ActionType[\"DISMISS_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n  | {\r\n      type: ActionType[\"REMOVE_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case actionTypes.ADD_TOAST:\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case actionTypes.UPDATE_TOAST:\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      }\r\n\r\n    case actionTypes.DISMISS_TOAST: {\r\n      const { toastId } = action\r\n\r\n      // Doing this as the `dismiss` action can be called on a toast that's already been removed.\r\n      if (toastId) {\r\n        return {\r\n          ...state,\r\n          toasts: state.toasts.map((t) =>\r\n            t.id === toastId ? { ...t, open: false } : t\r\n          ),\r\n        }\r\n      } else {\r\n        return {\r\n          ...state,\r\n          toasts: state.toasts.map((t) => ({\r\n            ...t,\r\n            open: false,\r\n          })),\r\n        }\r\n      }\r\n    }\r\n    case actionTypes.REMOVE_TOAST:\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        };\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n    default:\r\n      return state\r\n  }\r\n}\r\n\r\nconst listeners: ((state: State) => void)[] = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => listener(memoryState))\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId()\r\n\r\n  const update = (props: Partial<ToasterToast>) =>\r\n    dispatch({ type: actionTypes.UPDATE_TOAST, toast: { ...props, id } })\r\n  const dismiss = () => dispatch({ type: actionTypes.DISMISS_TOAST, toastId: id })\r\n\r\n  dispatch({\r\n    type: actionTypes.ADD_TOAST,\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) {\r\n          dismiss()\r\n        }\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    }\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId?: string) => dispatch({ type: actionTypes.DISMISS_TOAST, toastId }),\r\n  }\r\n}\r\n\r\nexport { useToast, toast }\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAMA,MAAM,cAAc;AASpB,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,UAAU,CAAC,OAAc;IAC7B,OAAQ,OAAO,IAAI;QACjB,KAAK,YAAY,SAAS;YACxB,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK,YAAY,YAAY;YAC3B,OAAO;gBACL,GAAG,KAAK;gBA<PERSON>,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK,YAAY,aAAa;YAAE;gBAC9B,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2FAA2F;gBAC3F,IAAI,SAAS;oBACX,OAAO;wBACL,GAAG,KAAK;wBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,UAAU;gCAAE,GAAG,CAAC;gCAAE,MAAM;4BAAM,IAAI;oBAE/C;gBACF,OAAO;oBACL,OAAO;wBACL,GAAG,KAAK;wBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;gCAC/B,GAAG,CAAC;gCACJ,MAAM;4BACR,CAAC;oBACH;gBACF;YACF;QACA,KAAK,YAAY,YAAY;YAC3B,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;QACF;YACE,OAAO;IACX;AACF;AAEA,MAAM,YAAwC,EAAE;AAEhD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS;AAC3C;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YAAE,MAAM,YAAY,YAAY;YAAE,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QAAE;IACrE,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM,YAAY,aAAa;YAAE,SAAS;QAAG;IAE9E,SAAS;QACP,MAAM,YAAY,SAAS;QAC3B,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;oBACT;gBACF;YACF;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM,YAAY,aAAa;gBAAE;YAAQ;IACrF;AACF", "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/src/lib/supabase/client.ts"], "sourcesContent": ["// src/lib/supabase/client.ts\r\nimport { createBrowserClient } from '@supabase/ssr';\r\n\r\nexport function createSupabaseBrowserClient() {\r\n  return createBrowserClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n  );\r\n}"], "names": [], "mappings": "AAAA,6BAA6B;;;;AAC7B;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/app/%28auth%29/login/page.tsx"], "sourcesContent": ["// src/app/(auth)/login/page.tsx\r\n'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useForm, SubmitHandler } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport * as z from 'zod';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { useToast } from '@/components/ui/use-toast';\r\nimport { createSupabaseBrowserClient } from '@/lib/supabase/client';\r\n// import { GradientText } from '@/components/vendor/gradient-text'; // Uncomment if you create this\r\n\r\nconst loginSchema = z.object({\r\n  email: z.string().email({ message: 'Invalid email address.' }),\r\n  password: z.string().min(1, { message: 'Password is required.' }), // Min 1 for login\r\n});\r\n\r\ntype LoginFormValues = z.infer<typeof loginSchema>;\r\n\r\nexport default function LoginPage() {\r\n  const supabase = createSupabaseBrowserClient();\r\n  const router = useRouter();\r\n  const { toast } = useToast();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n  } = useForm<LoginFormValues>({\r\n    resolver: zodResolver(loginSchema),\r\n  });\r\n\r\n  const onSubmit: SubmitHandler<LoginFormValues> = async (data) => {\r\n    setIsLoading(true);\r\n    try {\r\n      const { error } = await supabase.auth.signInWithPassword({\r\n        email: data.email,\r\n        password: data.password,\r\n      });\r\n\r\n      if (error) {\r\n        throw error;\r\n      }\r\n\r\n      toast({\r\n        title: 'Login Successful!',\r\n        description: 'Welcome back!',\r\n      });\r\n      router.push('/dashboard'); // Middleware will handle redirect after onAuthStateChange\r\n    } catch (error: any) { // Changed Error to any\r\n      console.error('Login error:', error);\r\n      toast({\r\n        title: 'Login Failed',\r\n        description: error.message || 'Invalid credentials. Please try again.',\r\n        variant: 'destructive',\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n  \r\n  const handleGoogleSignIn = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const { error } = await supabase.auth.signInWithOAuth({\r\n        provider: 'google',\r\n        options: {\r\n          redirectTo: `${window.location.origin}/api/auth/callback`,\r\n        },\r\n      });\r\n      if (error) throw error;\r\n    } catch (error: any) { // Changed Error to any\r\n      console.error('Google Sign-In error:', error);\r\n      toast({\r\n        title: 'Google Sign-In Failed',\r\n        description: error.message || 'Could not sign in with Google. Please try again.',\r\n        variant: 'destructive',\r\n      });\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className=\"w-full max-w-md\">\r\n      <CardHeader className=\"space-y-1 text-center\">\r\n        {/* <GradientText className=\"text-3xl font-bold\">Quikrsolutions</GradientText> */}\r\n        <CardTitle className=\"text-2xl\">Welcome Back!</CardTitle>\r\n        <CardDescription>\r\n          Enter your credentials to access your account\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"grid gap-4\">\r\n        <form onSubmit={handleSubmit(onSubmit)} className=\"grid gap-4\"> {/* Corrected form tag */}\r\n          <div className=\"grid gap-2\">\r\n            <Label htmlFor=\"email\">Email</Label>\r\n            <Input\r\n              id=\"email\"\r\n              type=\"email\"\r\n              placeholder=\"<EMAIL>\"\r\n              {...register('email')}\r\n              disabled={isLoading}\r\n            />\r\n            {errors.email && (\r\n              <p className=\"text-sm text-red-500\">{errors.email.message}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"grid gap-2\">\r\n            <Label htmlFor=\"password\">Password</Label>\r\n            <Input\r\n              id=\"password\"\r\n              type=\"password\"\r\n              placeholder=\"********\"\r\n              {...register('password')}\r\n              disabled={isLoading}\r\n            />\r\n            {errors.password && (\r\n              <p className=\"text-sm text-red-500\">{errors.password.message}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"flex items-center justify-end\">\r\n            <Link\r\n              href=\"/forgot-password\" // We'll create this page later\r\n              className=\"text-sm text-muted-foreground hover:underline\"\r\n            >\r\n              Forgot password?\r\n            </Link>\r\n          </div>\r\n          <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\r\n            {isLoading ? 'Signing In...' : 'Sign In'}\r\n          </Button>\r\n        </form>\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-0 flex items-center\">\r\n            <span className=\"w-full border-t\" />\r\n          </div>\r\n          <div className=\"relative flex justify-center text-xs uppercase\">\r\n            <span className=\"bg-card px-2 text-muted-foreground\">\r\n              Or continue with\r\n            </span>\r\n          </div>\r\n        </div>\r\n        <Button variant=\"outline\" className=\"w-full\" onClick={handleGoogleSignIn} disabled={isLoading}>\r\n          {/* Add Google Icon here later with lucide-react */}\r\n          Google\r\n        </Button>\r\n      </CardContent>\r\n      <CardFooter className=\"flex justify-center\">\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Don't have an account?{' '} {/* Escaped apostrophe */}\r\n          <Link href=\"/signup\" className=\"font-semibold text-primary hover:underline\">\r\n            Sign Up\r\n          </Link>\r\n        </p>\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;;AAGhC;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAeA,oGAAoG;AAEpG,MAAM,cAAc,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,EAAE;IAC3B,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAAyB;IAC5D,UAAU,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAAwB;AACjE;AAIe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAA2C,OAAO;QACtD,aAAa;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACvD,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;YAEA,IAAI,OAAO;gBACT,MAAM;YACR;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,OAAO,IAAI,CAAC,eAAe,0DAA0D;QACvF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;gBACpD,UAAU;gBACV,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBAC3D;YACF;YACA,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;YACX;YACA,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCAEpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW;;;;;;kCAChC,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;;4BAAa;0CAC7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACX,GAAG,SAAS,QAAQ;wCACrB,UAAU;;;;;;oCAEX,OAAO,KAAK,kBACX,8OAAC;wCAAE,WAAU;kDAAwB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACX,GAAG,SAAS,WAAW;wCACxB,UAAU;;;;;;oCAEX,OAAO,QAAQ,kBACd,8OAAC;wCAAE,WAAU;kDAAwB,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK,mBAAmB,+BAA+B;;oCACvD,WAAU;8CACX;;;;;;;;;;;0CAIH,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,WAAU;gCAAS,UAAU;0CAChD,YAAY,kBAAkB;;;;;;;;;;;;kCAGnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;;;;;;;;;;;;kCAKzD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;wBAAS,SAAS;wBAAoB,UAAU;kCAC9B;;;;;;;;;;;;0BAIxD,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAE,WAAU;;wBAAgC;wBACpB;wBAAI;sCAC3B,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAU,WAAU;sCAA6C;;;;;;;;;;;;;;;;;;;;;;;AAOtF", "debugId": null}}]}