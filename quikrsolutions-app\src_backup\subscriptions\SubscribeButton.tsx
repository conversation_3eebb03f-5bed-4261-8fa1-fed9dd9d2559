// src/components/subscriptions/SubscribeButton.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button'; // Adjust import path if needed
import { createCheckoutSession } from '@/app/actions/stripeActions'; // Adjust import path if needed
import { useRouter } from 'next/navigation'; // For client-side redirect
import { useToast } from '@/components/ui/use-toast'; // Assuming you use shadcn/ui toast

interface SubscribeButtonProps {
  priceId: string;
  planName: string;
}

export function SubscribeButton({ priceId, planName }: SubscribeButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleSubscribe = async () => {
    setIsLoading(true);
    try {
      const result = await createCheckoutSession({ priceId });

      if (result.error) {
        console.error('Error creating checkout session:', result.error);
        toast({
          title: "Subscription Error",
          description: result.error,
          variant: "destructive",
        });
      } else if (result.url) {
        // Redirect to Stripe Checkout
        router.push(result.url);
        // No need to setIsLoading(false) as we are navigating away
        return;
      } else {
         toast({
          title: "Subscription Error",
          description: "Could not initiate subscription. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('Unexpected error during subscription:', error);
      toast({
        title: "Subscription Error",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    }
    setIsLoading(false);
  };

  return (
    <Button onClick={handleSubscribe} disabled={isLoading} className="w-full">
      {isLoading ? 'Processing...' : `Subscribe to ${planName}`}
    </Button>
  );
}
