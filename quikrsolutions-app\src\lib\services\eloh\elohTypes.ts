// src/lib/services/eloh/elohTypes.ts

// == Generic Error Response ==
export interface ElohErrorResponse {
  status: 'error'; // To distinguish from success
  message: string; // User-friendly error message
  errorCode?: string; // Optional: A machine-readable error code
  details?: Record<string, any>; // Optional: More detailed error information
}

// == Crypto Payments (BTCPay Server via Eloh) ==

// Parameters for Quikrsolutions to send to Eloh to initiate a crypto payment
export interface InitiateElohCryptoPaymentParams {
  amountFiat: number;             // The amount in fiat currency
  fiatCurrency: string;           // The fiat currency code (e.g., "USD", "XCD")
  description: string;            // Description of the payment/order
  quikrsolutionsOrderId: string;  // Our internal unique order ID
  buyerEmail?: string;            // Optional: Customer's email for notifications
  redirectUrl: string;            // URL user is sent to after BTCPay interaction
  notificationUrl: string;        // URL Eloh/BTCPay sends webhooks to (our /api/webhooks/eloh/btcpay)
  // Potentially add: preferredCryptoCurrency?: string; // If user can select upfront
}

// Success response from <PERSON><PERSON> after successfully creating a BTCPay invoice
export interface ElohCryptoPaymentSuccessResponse {
  status: 'success';
  data: {
    elohTransactionId: string;    // Eloh's unique ID for this transaction attempt
    btcpayInvoiceId: string;      // The BTCPay Server Invoice ID
    btcpayInvoiceUrl: string;     // The URL for the user to make the payment
    expiresAt: string;            // ISO 8601 timestamp for when the invoice expires
    amountExpectedCrypto?: string;// Expected crypto amount (e.g., "0.00123") - might be set by BTCPay
    cryptoCurrency?: string;      // Expected crypto currency (e.g., "BTC") - might be set by BTCPay
  };
}

// Union type for the response of initiating a crypto payment
export type InitiateElohCryptoPaymentResult = ElohCryptoPaymentSuccessResponse | ElohErrorResponse;


// == Remittances (Stellar via Eloh) ==

// Parameters for Quikrsolutions to send to Eloh to get an exchange rate quote
export interface GetElohExchangeRateParams {
  fromCurrency: string; // e.g., "USD", "BTC" (if paying remittance fee with crypto)
  toCurrency: string;   // e.g., "XCD", "GHS" (recipient's currency)
  amount: number;       // Amount in fromCurrency
  // Potentially add: paymentMethod?: 'crypto' | 'stripe_balance' // How Quikrsolutions/user funds the remittance
}

// Success response from Eloh for an exchange rate quote
export interface ElohExchangeRateSuccessResponse {
  status: 'success';
  data: {
    rate: number;                     // The exchange rate (e.g., 1 USD = 2.70 XCD, rate would be 2.70)
    sourceAmount: number;             // The amount in the source currency
    destinationAmount: number;        // The calculated amount in the destination currency
    fee: number;                      // Fee amount in sourceCurrency
    totalCharged: number;             // sourceAmount (if amount was destination) or sourceAmount + fee
    fromCurrency: string;
    toCurrency: string;
    expiresAt: string;                // ISO 8601 timestamp for how long this quote is valid
    elohQuoteId?: string;             // Eloh's unique ID for this quote
  };
}
export type GetElohExchangeRateResult = ElohExchangeRateSuccessResponse | ElohErrorResponse;

// Parameters for Quikrsolutions to send to Eloh to initiate a remittance
export interface InitiateElohRemittanceParams {
  quoteId?: string;                 // Optional: ID of a previously obtained exchange rate quote
  amountToSend: number;            // Amount to be sent by the sender
  sendCurrency: string;            // Currency of amountToSend (e.g., "USD")
  receiveCurrency: string;         // Target currency for the recipient (e.g., "XCD")
  // If quoteId is not provided, Eloh might use current best rate or require these:
  // expectedReceiveAmount?: number;

  senderUserId: string;            // Quikrsolutions user ID of the sender
  // Sender details might be fetched by Eloh using senderUserId or provided here if needed by compliance

  recipientFirstName: string;
  recipientLastName: string;
  recipientCountry: string;        // ISO 2-letter country code
  // Recipient delivery details (will vary based on method)
  recipientPhoneNumber?: string;    // For SMS notifications or mobile money
  recipientEmail?: string;          // For email notifications
  recipientStellarAddress?: string; // If direct to Stellar wallet
  recipientBankName?: string;       // If bank deposit
  recipientBankAccountNumber?: string;
  recipientPickupLocationId?: string; // If cash pickup

  quikrsolutionsRemittanceId: string; // Our internal unique ID for this remittance
  purposeOfRemittance?: string;     // Optional, for compliance
  // paymentSourceNonce?: string;   // If Quikrsolutions is charging a card via Stripe for this and passing nonce to Eloh
}

// Success response from Eloh after successfully initiating a remittance
export interface ElohRemittanceSuccessResponse {
  status: 'success';
  data: {
    elohRemittanceId: string;       // Eloh's unique ID for this remittance
    quikrsolutionsRemittanceId: string; // Echo back our ID
    currentStatus: string;          // Initial status (e.g., "pending_funding", "processing")
    stellarTransactionId?: string; // Stellar network transaction ID (once submitted to network)
    estimatedDeliveryAt?: string;  // ISO 8601 timestamp
    // Potentially echo back some recipient details or next steps for sender
  };
}
export type InitiateElohRemittanceResult = ElohRemittanceSuccessResponse | ElohErrorResponse;

// Parameters for Quikrsolutions to send to Eloh to get remittance status
export interface GetElohRemittanceStatusParams {
  elohRemittanceId?: string;
  quikrsolutionsRemittanceId?: string; // Allow lookup by either ID
}

// Success response from Eloh for remittance status
export interface ElohRemittanceStatusSuccessResponse {
  status: 'success';
  data: {
    elohRemittanceId: string;
    quikrsolutionsRemittanceId: string;
    currentStatus: string; // e.g., "pending_funding", "processing", "in_transit", "ready_for_pickup", "completed", "failed", "refunded"
    statusHistory?: Array<{ status: string; timestamp: string; notes?: string }>;
    // Other relevant details like amounts, recipient info etc. might be included
    stellarTransactionId?: string;
    pickupCode?: string; // If applicable
  };
}
export type GetElohRemittanceStatusResult = ElohRemittanceStatusSuccessResponse | ElohErrorResponse;

export interface RemittanceStatusResponse { // Export this interface
  status: 'success';
  data: {
    elohRemittanceId: string;
    quikrsolutionsRemittanceId: string;
    currentStatus: string; // e.g., "pending_funding", "processing", "in_transit", "ready_for_pickup", "completed", "failed", "refunded"
    statusHistory?: Array<{ status: string; timestamp: string; notes?: string }>;
    // Other relevant details like amounts, recipient info etc. might be included
    stellarTransactionId?: string;
    pickupCode?: string; // If applicable
  };
}


// == Webhook Payloads (from Eloh to Quikrsolutions) ==
// These are what our /api/webhooks/eloh/* endpoints will receive

// Base structure for all Eloh webhooks
interface ElohWebhookBasePayload<TData> {
  eventType: string; // e.g., "crypto.invoice.confirmed", "remittance.status.updated"
  eventId: string;   // Unique ID for this webhook event itself
  timestamp: string; // ISO 8601
  data: TData;
}

// Data for Crypto Payment Webhook
export interface ElohCryptoWebhookData {
  elohTransactionId: string;
  btcpayInvoiceId: string;
  quikrsolutionsOrderId: string;
  status: string; // "processing", "confirmed", "failed_or_expired", etc.
  amountPaidCrypto?: string;
  cryptoCurrency?: string;
  transactionHash?: string;
  paidAt?: string;
}
export type ElohCryptoWebhookPayload = ElohWebhookBasePayload<ElohCryptoWebhookData>;


// Data for Remittance Webhook
export interface ElohRemittanceWebhookData {
  elohRemittanceId: string;
  quikrsolutionsRemittanceId: string;
  newStatus: string; // "funded", "in_transit", "ready_for_pickup", "completed", "failed"
  // Potentially include other details like stellarTransactionId, pickupCode when relevant
  reasonForFailure?: string;
}
export type ElohRemittanceWebhookPayload = ElohWebhookBasePayload<ElohRemittanceWebhookData>;
