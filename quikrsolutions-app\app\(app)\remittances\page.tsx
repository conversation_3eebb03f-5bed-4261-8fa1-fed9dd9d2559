'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useRealtimeRemittance } from '@/hooks/useRealtimeRemittance';

interface Remittance {
  id: string;
  amount: number;
  currency: string;
  recipientName: string;
  status: string; // e.g., 'pending', 'processing', 'completed', 'failed'
  createdAt: string;
}

export default function RemittancesHistoryPage() {
  const [remittances, setRemittances] = useState<Remittance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // TODO: Fetch remittance history from an API route or Supabase
  useEffect(() => {
    const fetchRemittances = async () => {
      try {
        // Replace with actual API call
        const mockRemittances: Remittance[] = [
          { id: '1', amount: 100, currency: 'USD', recipientName: '<PERSON> Smith', status: 'completed', createdAt: '2023-10-26T10:00:00Z' },
          { id: '2', amount: 50, currency: 'EUR', recipientName: 'Bob Johnson', status: 'processing', createdAt: '2023-10-25T15:30:00Z' },
          { id: '3', amount: 200, currency: 'GBP', recipientName: 'Charlie Brown', status: 'pending', createdAt: '2023-10-24T09:15:00Z' },
        ];
        setRemittances(mockRemittances);
      } catch (err: any) {
        setError('Failed to fetch remittances.');
        console.error('Error fetching remittances:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchRemittances();
  }, []);

  // Call the hook without an ID to listen for all updates (or filter as needed)
  // Note: For a history page, you might only want updates for remittances already displayed.
  // The current hook implementation filters by a single ID.
  // A more advanced hook or separate logic would be needed to manage updates for a list.
  // For now, we'll keep the hook call but the update logic needs to be adapted or the hook modified.
  // const { remittanceDetails: realtimeUpdate, loading: realtimeLoading, error: realtimeError } = useRealtimeRemittance();

  // TODO: Adapt the realtime update logic for the history page to update the list
  // useEffect(() => {
  //   if (realtimeUpdate) {
  //     setRemittances(currentRemittances => {
  //       const updatedRemittances = [...currentRemittances];
  //       const index = updatedRemittances.findIndex(r => r.id === realtimeUpdate.id);
  //       if (index !== -1) {
  //         // Update existing remittance
  //         updatedRemittances[index] = { ...updatedRemittances[index], ...realtimeUpdate };
  //       }
  //       // For history, we typically don't add new items via realtime, just update existing.
  //       return updatedRemittances;
  //     });
  //   }
  // }, [realtimeUpdate]);


  if (loading) {
    return <div className="container mx-auto py-8">Loading remittances...</div>;
  }

  if (error) {
    return <div className="container mx-auto py-8 text-red-500">{error}</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Remittance History</CardTitle>
        </CardHeader>
        <CardContent>
          {remittances.length === 0 ? (
            <p>No remittances found.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Currency</TableHead>
                    <TableHead>Recipient</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {remittances.map((remittance) => (
                    <TableRow key={remittance.id}>
                      <TableCell>{remittance.id}</TableCell>
                      <TableCell>{remittance.amount}</TableCell>
                      <TableCell>{remittance.currency}</TableCell>
                      <TableCell>{remittance.recipientName}</TableCell>
                      <TableCell>{remittance.status}</TableCell>
                      <TableCell>{new Date(remittance.createdAt).toLocaleDateString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
