'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useRealtimeRemittance } from '@/hooks/useRealtimeRemittance';
import { Database } from '@/types/supabase';
import { Loader2, Plus, Eye } from 'lucide-react';
import Link from 'next/link';

type RemittanceTransactionRow = Database['public']['Tables']['remittance_transactions']['Row'];

export default function RemittancesHistoryPage() {
  const [remittances, setRemittances] = useState<RemittanceTransactionRow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // TODO: Fetch remittance history from an API route or Supabase
  useEffect(() => {
    const fetchRemittances = async () => {
      try {
        console.log('Fetching remittances from /api/quikr/remittances/history');
        const response = await fetch('/api/quikr/remittances/history');

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('API response:', result);

        if (result.status === 'success') {
          setRemittances(result.data || []);
        } else {
          setError(result.message || 'Failed to fetch remittances');
          console.error('API returned error:', result);
        }
      } catch (err: any) {
        const errorMessage = err.message || 'Failed to fetch remittances';
        setError(errorMessage);
        console.error('Error fetching remittances:', {
          message: err.message,
          stack: err.stack,
          name: err.name
        });
      } finally {
        setLoading(false);
      }
    };

    fetchRemittances();
  }, []);

  // Call the hook without an ID to listen for all updates (or filter as needed)
  // Note: For a history page, you might only want updates for remittances already displayed.
  // The current hook implementation filters by a single ID.
  // A more advanced hook or separate logic would be needed to manage updates for a list.
  // For now, we'll keep the hook call but the update logic needs to be adapted or the hook modified.
  // const { remittanceDetails: realtimeUpdate, loading: realtimeLoading, error: realtimeError } = useRealtimeRemittance();

  // TODO: Adapt the realtime update logic for the history page to update the list
  // useEffect(() => {
  //   if (realtimeUpdate) {
  //     setRemittances(currentRemittances => {
  //       const updatedRemittances = [...currentRemittances];
  //       const index = updatedRemittances.findIndex(r => r.id === realtimeUpdate.id);
  //       if (index !== -1) {
  //         // Update existing remittance
  //         updatedRemittances[index] = { ...updatedRemittances[index], ...realtimeUpdate };
  //       }
  //       // For history, we typically don't add new items via realtime, just update existing.
  //       return updatedRemittances;
  //     });
  //   }
  // }, [realtimeUpdate]);


  const getStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'default';
      case 'processing': case 'in_transit': return 'secondary';
      case 'pending': case 'funded': return 'outline';
      case 'failed': case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading remittances...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500">
              <p>{error}</p>
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="mt-4"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Remittance History</h1>
        <Link href="/app/remittances/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Remittance
          </Button>
        </Link>
      </div>

      <Card>
        <CardContent className="pt-6">
          {remittances.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No remittances found.</p>
              <Link href="/app/remittances/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Send Your First Remittance
                </Button>
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Recipient</TableHead>
                    <TableHead>Send Amount</TableHead>
                    <TableHead>Receive Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {remittances.map((remittance) => (
                    <TableRow key={remittance.id}>
                      <TableCell>
                        {new Date(remittance.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {remittance.recipient_first_name} {remittance.recipient_last_name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {remittance.recipient_country}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {remittance.send_amount} {remittance.send_currency}
                      </TableCell>
                      <TableCell>
                        {remittance.receive_amount} {remittance.receive_currency}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(remittance.status)}>
                          {remittance.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Link href={`/app/remittances/track?id=${remittance.quikrsolutions_remittance_id}`}>
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4 mr-1" />
                            Track
                          </Button>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
