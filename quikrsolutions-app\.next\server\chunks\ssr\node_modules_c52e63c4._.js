module.exports = {

"[project]/node_modules/@supabase/node-fetch/lib/index.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/lib/index.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/node_modules/ws/wrapper.mjs [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_ws_58f5cae3._.js",
  "server/chunks/ssr/[root-of-the-server]__b8136ca7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/ws/wrapper.mjs [app-rsc] (ecmascript)");
    });
});
}}),

};