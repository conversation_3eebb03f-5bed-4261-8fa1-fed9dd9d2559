import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ShoppingCart, Heart, Star, DollarSign } from "lucide-react"

export default function MarketplacePage() {
  const products = [
    {
      id: 1,
      name: "Professional Website Design",
      category: "Digital Services",
      price: 599,
      seller: "WebCraft Studios",
      rating: 4.9,
      reviews: 67,
      image: "/api/placeholder/300/200",
      description: "Custom responsive website design with modern UI/UX"
    },
    {
      id: 2,
      name: "Organic Coffee Beans - 1kg",
      category: "Food & Beverage",
      price: 24.99,
      seller: "Mountain Roasters",
      rating: 4.7,
      reviews: 143,
      image: "/api/placeholder/300/200",
      description: "Premium organic coffee beans, freshly roasted"
    },
    {
      id: 3,
      name: "Business Consulting Session",
      category: "Professional Services",
      price: 150,
      seller: "BizGrow Consultants",
      rating: 4.8,
      reviews: 89,
      image: "/api/placeholder/300/200",
      description: "1-hour strategic business consultation with expert"
    },
    {
      id: 4,
      name: "Handcrafted Wooden Desk",
      category: "Furniture",
      price: 450,
      seller: "Artisan Woodworks",
      rating: 4.6,
      reviews: 34,
      image: "/api/placeholder/300/200",
      description: "Solid oak desk with modern minimalist design"
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Marketplace Hub</h1>
          <p className="text-muted-foreground">Buy and sell products and services</p>
        </div>
        <Button>Sell Something</Button>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <Input 
          placeholder="Search products and services..." 
          className="max-w-md"
        />
        <Button variant="outline">Categories</Button>
        <Button variant="outline">Price Range</Button>
        <Button variant="outline">Sort by</Button>
      </div>

      {/* Featured Categories */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="text-center p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="text-2xl mb-2">💻</div>
          <h3 className="font-medium">Digital Services</h3>
        </Card>
        <Card className="text-center p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="text-2xl mb-2">🏠</div>
          <h3 className="font-medium">Home & Garden</h3>
        </Card>
        <Card className="text-center p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="text-2xl mb-2">👔</div>
          <h3 className="font-medium">Professional Services</h3>
        </Card>
        <Card className="text-center p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="text-2xl mb-2">🎨</div>
          <h3 className="font-medium">Arts & Crafts</h3>
        </Card>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <Card key={product.id} className="hover:shadow-lg transition-shadow">
            <div className="aspect-video bg-muted rounded-t-lg flex items-center justify-center">
              <span className="text-muted-foreground">Product Image</span>
            </div>
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-base line-clamp-2">{product.name}</CardTitle>
                  <Badge variant="secondary" className="mt-1 text-xs">{product.category}</Badge>
                </div>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Heart className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <CardDescription className="text-sm line-clamp-2">
                {product.description}
              </CardDescription>
              
              <div className="flex items-center gap-1 text-sm">
                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">{product.rating}</span>
                <span className="text-muted-foreground">({product.reviews})</span>
              </div>
              
              <div className="text-sm text-muted-foreground">
                by {product.seller}
              </div>
              
              <div className="flex items-center justify-between pt-2">
                <div className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  <span className="text-lg font-bold">{product.price}</span>
                </div>
                <Button size="sm">
                  <ShoppingCart className="h-4 w-4 mr-1" />
                  Add to Cart
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      <div className="text-center">
        <Button variant="outline">Load More Products</Button>
      </div>
    </div>
  )
}
