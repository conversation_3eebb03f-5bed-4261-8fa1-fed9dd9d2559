{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/quikrsolutions_platform/quikrsolutions-app/app/api/test/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nexport async function GET() {\n  console.log('Test API route called');\n  return NextResponse.json({\n    status: 'success',\n    message: 'Test API route is working',\n    timestamp: new Date().toISOString()\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,QAAQ,GAAG,CAAC;IACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,QAAQ;QACR,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;IACnC;AACF", "debugId": null}}]}