// src/lib/supabase/server.ts
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js'; // Changed from require

export { createServerClient } from '@supabase/ssr'; // Export createServerClient

// For Server Components, Route Handlers
export async function createSupabaseServerClient() {
  const cookieStore = await cookies();
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set({ name, value: '', ...options });
        },
      },
    }
  );
}

// For Route Handlers and Server Actions where you pass the cookie store explicitly,
// for example, in a Server Action where `cookies()` might not be directly available
// or when working with specific cookie manipulation logic.
// Reverting to a simpler type for cookieStore and using set for remove.
export async function createSupabaseServerClientWithCookies(cookieStore: any) { // Changed any to any as per linter error
    return createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
            cookies: {
                get(name: string) {
                return cookieStore.get(name)?.value
                },
                set(name: string, value: string, options: CookieOptions) {
                cookieStore.set({ name, value, ...options })
                },
                remove(name: string, options: CookieOptions) {
                cookieStore.set({ name, value: '', ...options })
                },
            },
        }
    )
}

// For Admin-level operations
export function createSupabaseAdminClient() {
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined. This client is for admin operations only.');
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
      global: {
        headers: { 'x-supabase-api-key': process.env.SUPABASE_SERVICE_ROLE_KEY },
      },
    }
  );
}
