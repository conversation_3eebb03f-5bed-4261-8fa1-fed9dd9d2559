import { NextResponse } from 'next/server';
import crypto from 'crypto';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

const ELOH_WEBHOOK_SECRET = process.env.ELOH_WEBHOOK_SECRET;

async function verifyWebhookSignature(request: Request, signature: string | null, sharedSecret: string): Promise<boolean> {
  if (!signature) {
    console.warn('Missing signature');
    return false; // Missing signature
  }

  const body = await request.text(); // Get raw request body

  const hmac = crypto.createHmac('sha256', sharedSecret);
  hmac.update(body);
  const calculatedSignature = hmac.digest('hex');

  // Timing-safe comparison
  try {
    return crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(calculatedSignature, 'hex'));
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
}

export async function POST(request: Request) {
  const signature = request.headers.get('X-Eloh-Signature');
  const sharedSecret = ELOH_WEBHOOK_SECRET;

  if (!sharedSecret) {
    console.error('Missing ELOH_WEBHOOK_SECRET');
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const isValid = await verifyWebhookSignature(request, signature, sharedSecret);

  if (!isValid) {
    console.warn('Invalid webhook signature');
    return new Response(JSON.stringify({ error: 'Invalid signature' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    const body = await request.json();
    const { eventType, data } = body;

    console.log('Received Eloh webhook event:', { eventType, data });

    const supabaseAdmin = createSupabaseAdminClient();

    switch (eventType) {
      case 'invoice_processing': {
        const { elohTransactionId, btcpayInvoiceId, amountPaidCrypto, cryptoCurrency } = data;

        console.log('Processing invoice:', btcpayInvoiceId || elohTransactionId);

        const { error } = await supabaseAdmin
          .from('eloh_crypto_transactions')
          .update({
            status: 'processing',
            amount_paid_crypto: amountPaidCrypto,
            crypto_currency: cryptoCurrency,
          })
          .or(`eloh_transaction_id.eq.${elohTransactionId},btcpay_invoice_id.eq.${btcpayInvoiceId}`);

        if (error) {
          console.error('Error updating transaction status:', error);
          return new Response(JSON.stringify({ error: 'Database update failed' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }

        console.log('Invoice processing status updated successfully.');
        break;
      }
      case 'invoice_confirmed': {
        const { elohTransactionId, btcpayInvoiceId, amountPaidCrypto, cryptoCurrency, transactionHash, paidAt } = data;

        console.log('Confirming invoice:', btcpayInvoiceId || elohTransactionId);

        const { error } = await supabaseAdmin
          .from('eloh_crypto_transactions')
          .update({
            status: 'confirmed',
            amount_paid_crypto: amountPaidCrypto,
            crypto_currency: cryptoCurrency,
            transaction_hash: transactionHash,
            paid_at: paidAt,
          })
          .or(`eloh_transaction_id.eq.${elohTransactionId},btcpay_invoice_id.eq.${btcpayInvoiceId}`);

        if (error) {
          console.error('Error updating transaction status:', error);
          return new Response(JSON.stringify({ error: 'Database update failed' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }

        console.log('Invoice confirmed status updated successfully.');
        // Placeholder for post-payment fulfillment logic
        console.log('Placeholder: Implement post-payment fulfillment logic here.');
        break;
      }
      case 'invoice_failed_or_expired': {
        const { elohTransactionId, btcpayInvoiceId, status } = data;

        console.log('Invoice failed/expired:', btcpayInvoiceId || elohTransactionId, 'Status:', status);

        const { error } = await supabaseAdmin
          .from('eloh_crypto_transactions')
          .update({
            status: status,
          })
          .or(`eloh_transaction_id.eq.${elohTransactionId},btcpay_invoice_id.eq.${btcpayInvoiceId}`);

        if (error) {
          console.error('Error updating transaction status:', error);
          return new Response(JSON.stringify({ error: 'Database update failed' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }

        console.log('Invoice failed/expired status updated successfully.');
        break;
      }
      default:
        console.warn('Unknown event type:', eventType);
        return new Response(JSON.stringify({ error: 'Unknown event type' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error: any) {
    console.error('Error processing webhook:', error);
    return new Response(JSON.stringify({ error: 'Webhook processing failed', details: error.message }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
