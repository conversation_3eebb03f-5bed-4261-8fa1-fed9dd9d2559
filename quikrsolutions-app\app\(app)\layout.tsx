import { createSupabaseServerClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import PrimaryNavbar from '@/components/core/PrimaryNavbar'
import SecondaryNavbar from '@/components/core/SecondaryNavbar'
import { AuthProvider } from '@/contexts/authcontext'
import { Toaster } from '@/components/ui/sonner'
import { ThemeProvider } from 'next-themes'

export default async function AppLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = createSupabaseServerClient()
  
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/auth/login')
  }

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <AuthProvider>
        <div className="min-h-screen bg-background">
          <PrimaryNavbar />
          <SecondaryNavbar />
          <main className="container mx-auto py-6 px-4">
            {children}
          </main>
          <Toaster richColors position="top-right" />
        </div>
      </AuthProvider>
    </ThemeProvider>
  )
}
